<script setup lang="ts">
import {
  ref,
  defineProps,
  defineEmits,
  watch,
  reactive,
  shallowRef,
  onMounted,
  onUnmounted,
  nextTick,
  computed
} from 'vue';
import { groupWebSocketData } from '@/utils';
import Trace<PERSON>hart from './trace-chart/index.vue';
import BoxPlot<PERSON>hart from './box-plot-chart/index.vue';
import SummaryChart from './summary-chart/index.vue';
import MetrologyChart from './metrology-chart/index.vue';
import { useSocketChartData } from '@/websocket';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { cloneDeep } from 'lodash-es';
import { getHttpWebsocketChart, getHttpOrWebSocket, getSchemaMeta } from '@futurefab/ui-sdk-api';
import { cmMainInfo as buttonIds } from '@/log-config';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { Splitpan<PERSON>, Pane } from 'splitpanes';
import type { Cm<PERSON>hartOpenFrom, CmChartType } from './interface';
import { usePaneHeights } from './usePaneHeights';
// import MockWebsocketData from '@/mock/WebSocketParams.json';
// import MockData0 from '@/mock/websocket0.json';
// import MockData from '@/mock/websocket1.json';
// import MockWebsocketData from '@/mock/mock-test.json';

export interface Props {
  showCharts: boolean;
  boxDetail: {
    commonGroupConfigVO: GroupConfigVO;
    // Normalization
    useDataNormalization: boolean;
    // match
    traceMatchingType: string;
    tableData: any[];
  };
  unMatchStepInfo: any;
  waferData?: any[];
  hasMetrology?: boolean;
  tableColumns?: any[];
  websocketParams?: any;
  filteredStep?: string[];
  allSteps?: string[];
  splitLegend?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  showCharts: false,
  boxDetail: () => ({
    commonGroupConfigVO: new GroupConfigVO(),
    useDataNormalization: false,
    traceMatchingType: 'ALL',
    tableData: []
  }),
  unMatchStepInfo: {},
  waferData: () => [],
  websocketParams: () => ({}),
  splitLegend: true
});
const emits = defineEmits(['sendOverlayData', 'changeColumn']);
// 整体chatsDiv宽高不变
const cmChartResBoxRef = ref<HTMLDivElement | null>(null);
// change recipe step
const stepValue = ref<string>('1');
const stateValue = ref<string>('MEDIAN');
// 全部step数据
const stepInfo = defineModel<any[]>('stepInfo');
const summaryStep = ref<any[]>([]);
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const opneForm = reactive<CmChartOpenFrom>({
  trace: true,
  summary: true,
  boxplot: true,
  metrology: true
});
const _hasMetrology = computed(() => props.hasMetrology);
const { paneHeights, setPane } = usePaneHeights(opneForm, cmChartResBoxRef as any, _hasMetrology);
// websocket
const chartLoading = ref(false);
const isWebsocket = ref(false);
const { sendSocket, socketChartData } = useSocketChartData();
let httpChartData = reactive<any>({
  groupData: {},
  initData: [],
  contextIndex: {}
});
const metaData = ref<any>({});
onMounted(async () => {
  const { data } = await getHttpOrWebSocket({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-http-or-webSocket'
    }
  });
  isWebsocket.value = data;
  const schemaMeta = await getSchemaMeta({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-schema-meta'
    }
  });
  metaData.value = schemaMeta.data;
});
const groupData: any = shallowRef({});
const initData = shallowRef<any>([]);
const contextIndex: any = shallowRef({});
// 当前选中的行
const currentRowInfo = ref<string[]>([]);
const groupConfigIds = ref<number[]>([]);
const traceCheckData = shallowRef<any[]>([]);
const getHttpChartData = async (params: any) => {
  chartLoading.value = true;
  const httpServerChartData = await getHttpWebsocketChart({
    bodyParams: params,
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-http-websocket'
    }
  });
  if (httpServerChartData?.status === 'SUCCESS') {
    const data = httpServerChartData?.data;
    // const data = MockWebsocketData?.data;
    if (data?.traceDataBody?.length >= data?.traceDataHeader?.size) {
      // 图表数据赋值
      groupData.value = data?.traceDataHeader;
      initData.value = groupWebSocketData(data?.traceDataBody);
      contextIndex.value = data?.contextIndex;
    }
    chartLoading.value = false;
  } else {
    groupData.value = {};
    initData.value = [];
    contextIndex.value = {};
    chartLoading.value = false;
  }
};
const handleSendSocket = async (params: any) => {
  traceCheckData.value = [];
  if (isWebsocket.value) {
    // socket版本
    chartLoading.value = true;
    sendSocket(
      cloneDeep(params),
      () => {
        // tableData
        if (socketChartData?.chartData?.length >= socketChartData?.groupData?.size) {
          // 图表数据赋值
          groupData.value = socketChartData?.groupData;
          initData.value = groupWebSocketData(socketChartData.chartData);
          contextIndex.value = socketChartData?.contextIndex;
        }
        chartLoading.value = false;
      },
      () => {
        groupData.value = {};
        initData.value = [];
        contextIndex.value = {};
        chartLoading.value = false;
      }
    );
  } else {
    // http版本
    getHttpChartData(cloneDeep(params));
  }
  // 无法连接上时，关闭loading
  setTimeout(() => {
    chartLoading.value = false;
  }, 10000);
};
// overlay the chart
const overlayFlag = ref<any>({});
const handleOverlaySendSocket = async (params: any) => {
  overlayFlag.value = {};

  // 是否来自 cm-overlay-trace-chart 的请求
  const isOverlayTraceChart = params.overlay === true && params.paramAliasList?.length > 1;

  if (isOverlayTraceChart) {
    await handleOverlayTraceChartRequests(params);
  } else if (isWebsocket.value) {
    sendSocket(
      cloneDeep(params),
      () => {
        overlayFlag.value = params;
      },
      () => {
        overlayFlag.value = params;
      }
    );
  } else {
    const httpServerChartData = await getHttpWebsocketChart({
      bodyParams: cloneDeep(params),
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-http-websocket-chart'
      }
    });
    if (httpServerChartData?.status === 'SUCCESS') {
      overlayFlag.value = params;
      const data = httpServerChartData?.data;
      httpChartData.groupData = data?.traceDataHeader;
      httpChartData.chartData = data?.traceDataBody;
      httpChartData.contextIndex = data?.contextIndex;
    } else {
      httpChartData.groupData = {};
      httpChartData.chartData = [];
      httpChartData.contextIndex = {};
      overlayFlag.value = params;
    }
  }
};

// cm-overlay-trace-chart 的批量参数请求处理
const handleOverlayTraceChartRequests = async (params: any) => {
  const { paramAliasList, ...baseParams } = params;
  const totalParams = paramAliasList.length;

  // 分批处理，每批最多5个参数
  const batchSize = 5;
  const batches = [];
  for (let i = 0; i < paramAliasList.length; i += batchSize) {
    batches.push(paramAliasList.slice(i, i + batchSize));
  }

  // 创建所有批次的请求Promise
  const allBatchPromises = batches.map(async (batch, batchIndex) => {
    const batchRequest = {
      ...baseParams,
      paramAliasList: batch, // 批量请求多个参数（最多5个）
      overlay: true,
      requestIndex: batchIndex,
      totalCount: totalParams
    };

    try {
      const httpServerChartData = await getHttpWebsocketChart({
        bodyParams: cloneDeep(batchRequest),
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'get-http-websocket-chart'
        }
      });

      if (httpServerChartData?.status === 'SUCCESS') {
        emits('sendOverlayData', {
          groupData: httpServerChartData?.data?.traceDataHeader,
          chartData: httpServerChartData?.data?.traceDataBody,
          contextIndex: httpServerChartData?.data?.contextIndex
        });
        return true;
      }
      return false;
    } catch (error) {
      // 批次请求出错
      return false;
    }
  });

  // 等待所有批次完成
  await Promise.all(allBatchPromises);

  overlayFlag.value = params;
};

watch(httpChartData, () => {
  if (overlayFlag.value?.overlay && !isWebsocket.value) emits('sendOverlayData', httpChartData);
});
watch(socketChartData, () => {
  if (overlayFlag.value?.overlay && isWebsocket.value) emits('sendOverlayData', socketChartData);
});
watch(
  [() => props.boxDetail.tableData, () => props.unMatchStepInfo?.allColInfo, groupConfigIds],
  () => {
    const unMatchingRecipeSteps = new Set<string>();
    const warningRecipeSteps = new Set<string>();
    let allColInfo = new Map();
    if (props.unMatchStepInfo?.allColInfo) {
      allColInfo = props.unMatchStepInfo?.allColInfo;
    } else {
      // 默认选中第一行数据
      allColInfo = props.boxDetail.tableData.slice(3, 4)?.[0]?.allColInfo;
    }
    allColInfo?.forEach((deepOne: any, key: any) => {
      if (deepOne?.unmatchingRecipeSteps && deepOne.unmatchingRecipeSteps.length > 0) {
        deepOne.unmatchingRecipeSteps?.forEach((deepTwo: any) => {
          if (
            (!props.unMatchStepInfo?.isClick && groupConfigIds.value?.length === 0) ||
            groupConfigIds.value?.includes(key)
          ) {
            unMatchingRecipeSteps.add(deepTwo.recipeStepId);
          }
        });
      } else if (deepOne?.warningRecipeSteps && deepOne.warningRecipeSteps.length > 0) {
        deepOne.warningRecipeSteps?.forEach((deepTwo: any) => {
          if (
            (!props.unMatchStepInfo?.isClick && groupConfigIds.value?.length === 0) ||
            groupConfigIds.value?.includes(key)
          ) {
            warningRecipeSteps.add(deepTwo.recipeStepId);
          }
        });
      }
    });
    currentRowInfo.value =
      unMatchingRecipeSteps.size > 0
        ? [...unMatchingRecipeSteps]
        : warningRecipeSteps.size > 0
          ? [...warningRecipeSteps]
          : [];
  },
  { immediate: true, flush: 'post' }
);
// 点击不同的单元格，显示其对应的chart图
watch([() => props.unMatchStepInfo?.groupConfigId, () => props.unMatchStepInfo?.isClick], () => {
  const referenceIds: number[] = [];
  const ids: number[] = [];
  props.boxDetail?.commonGroupConfigVO?.groupConfigs.forEach((groupConfig: any) => {
    if (groupConfig?.reference) {
      referenceIds.push(groupConfig.id);
    }
    ids.push(groupConfig.id);
  });
  if (
    props.unMatchStepInfo?.isClick &&
    props.unMatchStepInfo?.groupConfigId &&
    !referenceIds.includes(props.unMatchStepInfo?.groupConfigId)
  ) {
    groupConfigIds.value = [props.unMatchStepInfo?.groupConfigId].concat(referenceIds);
  } else {
    groupConfigIds.value = ids;
  }
});
watch(
  [() => groupData.value?.stepInfo, () => props.allSteps, () => props.filteredStep],
  () => {
    const temp = props.allSteps?.map((step: any) => ({
      step,
      color: currentRowInfo.value.includes(step) ? 'red' : ''
    })) || [];
    if (props.filteredStep?.length) {
      summaryStep.value = [...temp]?.filter(({ step }: any) => 
        props.filteredStep?.includes(step)
      ) || [];
    } else {
      summaryStep.value = temp ? [...temp] : [];
    }
    stepInfo.value = temp;
    if (!stepValue.value || !summaryStep.value?.find(item => stepValue.value === item.step)) { // stepvalue有值，则不变
      // 默认选中第一个step，若有unMatch数组，选择unMatch数组的第一个step
      if (currentRowInfo.value?.length > 0) {
        stepValue.value = currentRowInfo.value?.[0];
      } else {
        stepValue.value = summaryStep.value?.[0]?.step;
      }
    }
  },
  { immediate: true }
);

const clearAllChartData = () => {
  initData.value = [];
  groupData.value = {};
  contextIndex.value = {};
  chartLoading.value = false;
};

const hasChartData = () => {
  return initData.value && initData.value.length > 0;
};

let observer: ResizeObserver | null = null;
let resizeTimer: any = null;
const chartBoxWidth = ref<number>(0);
const chartBoxHeight = ref<number>(0);
// temp
const chartBoxWidthTemp = ref<number>(0);
const chartBoxHeightTemp = ref<number>(0);
const setWidthHeight = () => {
  chartBoxWidth.value = chartBoxWidthTemp.value;
  chartBoxHeight.value = chartBoxHeightTemp.value;
};
onMounted(async () => {
  if (!cmChartResBoxRef.value) return;
  observer = new ResizeObserver(handleResize);
  observer.observe(cmChartResBoxRef.value);
});
const handleResize = (entries: any) => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect;
      chartBoxWidthTemp.value = width;
      chartBoxHeightTemp.value = height;
    }
  }, 0);
};
onUnmounted(() => {
  // 在组件销毁前取消观察
  if (observer) observer.disconnect();
});
watch([chartBoxWidthTemp, chartBoxHeightTemp], () => setWidthHeight());

// screenshot
const getUnmatchedStepList = () => {
  return (stepInfo.value ?? [])
    .filter((item: any) => item.color == 'red')
    .map((item: any) => item.step);
};

const switchStep = async (step: string) => {
  stepValue.value = step;
};

const getChartContainer = () => {
  return cmChartResBoxRef.value;
};
const getPaneParams = (type: CmChartType) => {
  return {
    size: paneHeights.value?.[type]?.height,
    minSize: paneHeights.value?.[type]?.minHeight,
    maxSize: paneHeights.value?.[type]?.maxHeight
  };
};
const traceChartRef = ref();
const metrologyChartRef = ref();
const pointClick = (event: any) => {
  traceChartRef.value?.highlight(event);
};
const changeColumn = (event: any) => emits('changeColumn', event);

// 暴露 metrology 相关方法给截图页面调用
const switchMetrologyParam = (param: string) => {
  metrologyChartRef.value?.switchMetrologyParam(param);
};

const getMetrologyOptions = () => {
  return metrologyChartRef.value?.getMetrologyOptions() || [];
};

defineExpose({
  handleSendSocket,
  clearAllChartData,
  hasChartData,
  handleOverlaySendSocket,
  getUnmatchedStepList,
  switchStep,
  getChartContainer,
  switchMetrologyParam,
  getMetrologyOptions
});
const baseClass = 'cm-chart-result';
</script>

<template>
  <div
    ref="cmChartResBoxRef"
    v-isLoading="{
      isShow: chartLoading,
      hasButton: false,
      title: 'Loading...'
    }"
    :class="baseClass + '-chart-box'"
  >
    <Splitpanes v-if="initData.length > 0" class="default-theme" horizontal>
      <pane v-bind="getPaneParams('trace')">
        <div :class="baseClass + '-chart-item'">
          <trace-chart
            ref="traceChartRef"
            v-model:checkData="traceCheckData"
            v-model:chartBoxWidth="chartBoxWidth"
            v-model:chartBoxHeight="chartBoxHeight"
            v-model:open="opneForm.trace"
            :chart-data="initData"
            :group-data="groupData"
            :box-detail="props.boxDetail"
            :current-row-info="currentRowInfo"
            :ids="groupConfigIds"
            :context-index="contextIndex"
            :metaData="metaData"
            :stepInfo="stepInfo"
            :tableColumns="tableColumns"
            :websocketParams="websocketParams"
            :split-legend="props.splitLegend"
            @changeColumn="changeColumn"
          >
            <template #title><span>Trace Chart</span></template>
          </trace-chart>
        </div>
      </pane>
      <pane v-bind="getPaneParams('summary')">
        <div :class="baseClass + '-chart-item'">
          <summary-chart
            v-model:chartBoxWidth="chartBoxWidth"
            v-model:chartBoxHeight="chartBoxHeight"
            v-model:open="opneForm.summary"
            v-model:stateValue="stateValue"
            :active-step="stepValue"
            :three-chats-div="cmChartResBoxRef || null"
            :chart-data="groupData"
            :common-group-config-v-o="props.boxDetail.commonGroupConfigVO"
            :ids="groupConfigIds"
            :param-alias="initData?.[0]?.paramAlias"
            :context-index="contextIndex"
            :metaData="metaData"
            :tableColumns="tableColumns"
            :split-legend="props.splitLegend"
            @point-click="pointClick"
          >
            <template #afterTitle>
              <a-select
                :key="stepInfo"
                v-model:value="stepValue"
                show-search
                placeholder="Select Step"
                :filter-option="filterOption"
                style="width: 118px"
                :show-arrow="true"
              >
                <a-select-option v-for="({ step, color }, i) in summaryStep" :key="i" :value="step">
                  <span :style="`color: ${color}`">{{ step }}</span>
                </a-select-option>
              </a-select>
            </template>
          </summary-chart>
        </div>
      </pane>
      <pane v-if="_hasMetrology" v-bind="getPaneParams('metrology')">
        <div :class="baseClass + '-chart-item'">
          <MetrologyChart
            ref="metrologyChartRef"
            v-model:open="opneForm.metrology"
            :summaryData="groupData"
            :waferData="waferData"
            :common-group-config-v-o="props.boxDetail.commonGroupConfigVO"
            :ids="groupConfigIds"
            :param-alias="initData?.[0]?.paramAlias"
            :metaData="metaData"
            :summaryStep="stepValue"
            :summaryType="stateValue"
            :context-index="contextIndex"
            :tableColumns="tableColumns"
            :split-legend="props.splitLegend"
          >
            <template #afterTitle>
              <a-select
                :key="stepInfo"
                v-model:value="stepValue"
                show-search
                placeholder="Select Step"
                :filter-option="filterOption"
                style="width: 118px"
                :show-arrow="true"
              >
                <a-select-option v-for="({ step, color }, i) in stepInfo" :key="i" :value="step">
                  <span :style="`color: ${color}`">{{ step }}</span>
                </a-select-option>
              </a-select>
            </template>
          </MetrologyChart>
        </div>
      </pane>
      <pane v-bind="getPaneParams('boxplot')">
        <div :class="baseClass + '-chart-item'">
          <box-plot-chart
            v-model:chartBoxWidth="chartBoxWidth"
            v-model:chartBoxHeight="chartBoxHeight"
            v-model:open="opneForm.boxplot"
            :active-step="stepValue"
            :chart-data="initData"
            :group-data="groupData"
            :common-group-config-v-o="props.boxDetail.commonGroupConfigVO"
            :ids="groupConfigIds"
            :copy-chart-title="`Boxplot Chart，Recipe Step：${Math.floor(
              Number(stepValue)
            )}，${initData?.[0]?.paramAlias}`"
            :context-index="contextIndex"
            :metaData="metaData"
            :tableColumns="tableColumns"
            :split-legend="props.splitLegend"
          >
          </box-plot-chart>
        </div>
      </pane>
    </Splitpanes>
    <div v-else class="nodata-chart-box"><NoData :border="false" /></div>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-chart-result {
  &-chart-box {
    overflow: hidden;
    width: 100%;
    height: 100%;
    border: 1px @border-color solid;
    .nodata-chart-box {
      width: 100%;
      height: 100%;
      .no-data {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: 700;
        color: @td-font-color;
        text-align: center;
      }
    }
  }
  &-chart-item {
    width: 100%;
    height: 100%;
  }
}
</style>
