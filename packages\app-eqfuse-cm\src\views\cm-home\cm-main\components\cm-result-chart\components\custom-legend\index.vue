<script setup lang="ts">
import { watch, shallowRef, ref, nextTick, computed } from 'vue';
import { EesButtonTip, setHeaderSelectFilter, showWarning } from '@futurefab/ui-sdk-ees-basic';
import type { LineItem, CustomLegendDataParams, CustomLegendData } from './interface';
import {
  getLegendBy,
  getCustomLegendOptions,
  getCustomLegendFilterField,
  getCustomLegendValidRules,
  getUploadData
} from './config';
import { t } from '@futurefab/vxe-table';
import moment from 'moment';
import { CHART_CONSTANT } from '@/constant/charts';
import ColorSelect from '@/components/color-select/index.vue';
import { cloneDeep } from 'lodash-es';
import { useCustomLegendStore } from '@futurefab/ui-sdk-stores';
import { saveCustomLegendData } from '@futurefab/ui-sdk-api';
import { message } from '@futurefab/ant-design-vue';

const customLegendStore = useCustomLegendStore();

const props = defineProps<{
  data: any[];
  groupColumns: any[];
  websocketParams: any;
  isLoop: boolean;
  hiddenCheckbox: boolean;
}>();
// 添加标志位防止循环调用
const isUpdatingStore = ref(false);
// 安全更新 store 的函数
const safeUpdateStore = (data: {
  legendByCustom: boolean;
  legendBy: string;
  lineItems: LineItem[];
}) => {
  isUpdatingStore.value = true;
  customLegendStore.setCustomLegendData({
    ...data,
    lineItems: data.lineItems.map((item: any) => {
      delete item?._X_ROW_KEY;
      return item;
    })
  });
  nextTick(() => {
    isUpdatingStore.value = false;
  });
};
const emit = defineEmits(['filterChart']);

const visible = ref(false);
const xGrid = ref();
const loading = ref<boolean>(false);
const options = getCustomLegendOptions(props.isLoop, props.hiddenCheckbox);
const validRules = ref(getCustomLegendValidRules(xGrid) as any);
// 弹窗数据
const legendByCustom = ref<boolean>(false);
const legendBy = ref<string>('');
// 选中的数据
const lineItems = ref<LineItem[]>([]);
// custom legend表格数据
const lineItemsTable = ref<LineItem[]>([]);
const lineItemsTableTemp = shallowRef<LineItem[]>([]);

const uniqueLineItemsTable = computed(() => {
  return lineItemsTable.value.filter(
    (item: LineItem, index: number, self: LineItem[]) =>
      index === self.findIndex((t: LineItem) => t.contextKey === item.contextKey)
  );
});
// 选中的数据
const handleCheckData = () => {
  const checkRecords = xGrid.value.getCheckboxRecords(true);
  if (lineItems.value) lineItems.value = checkRecords;
};
// 根据legendBy修改customLegendName为对应的值
const changeCustomLegendName = (key: keyof LineItem) => {
  lineItemsTable.value.forEach((item: LineItem) => {
    item.customLegendName = item[key] || item?.customLegendName;
  });
};
// 修改legendBy
const handleLegendByChange = (value: keyof LineItem) => {
  if (value) {
    changeCustomLegendName(value);
  } else {
    changeCustomLegendName('key');
  }
};
const tableEvent = {
  checkboxAll: (val: any) => {
    handleCheckData();
  },
  checkboxChange: (val: any) => {
    handleCheckData();
  }
};
const setHeader = () => {
  setHeaderSelectFilter({
    xGrid: xGrid,
    tableData: xGrid.value?.getTableData()?.fullData,
    columnFieldList: getCustomLegendFilterField()
  });
};
const reloadFilterHeader = () => {
  setTimeout(() => {
    setHeader();
  });
};
const handleOpen = () => {
  visible.value = true;
  // 初始化，保证数据一致性
  initCustomModel(props.data);
  setTimeout(() => {
    if (xGrid.value) {
      const { customLegendData } = customLegendStore;
      // 处理缓存数据
      legendByCustom.value = customLegendData.legendByCustom;
      legendBy.value = customLegendData.legendBy;
      let temp = [];
      const tableFullData = xGrid.value.getTableData()?.fullData;
      if (customLegendData.lineItems.length > 0) {
        const storeSelectData = customLegendData.lineItems.filter((item: any) => item.isChecked);
        temp = tableFullData.filter((item: any) =>
          storeSelectData.find((storeItem: any) => storeItem.contextKey === item.contextKey)
        );
      } else {
        temp = tableFullData;
      }
      xGrid.value?.setCheckboxRow(temp, true);
      lineItems.value = temp;
      reloadFilterHeader();
    }
  });
};
// 颜色选择
const uploadColor = (row: any, color: string) => {
  row.color = color;
  // 相同legendName的颜色统一修改
  lineItemsTable.value.forEach((item: LineItem) => {
    if (item.customLegendName === row.customLegendName) {
      item.color = color;
    }
  });
};
const handleCancel = () => {
  visible.value = false;
};
const handleOk = async () => {
  loading.value = true;
  const { updateRecords } = getUploadData(xGrid, lineItemsTableTemp.value);
  const error = await xGrid.value?.validate(updateRecords);
  if (lineItems.value.length === 0) {
    showWarning('cm.tips.noFilterTrace');
    loading.value = false;
    return;
  }
  if (!error) {
    const saveData = lineItemsTable.value.map((item: any) => {
      delete item?._X_ROW_KEY;
      return {
        ...item,
        isChecked: lineItems.value.includes(item)
      };
    });
    // 保存数据
    customLegendStore.setCustomLegendData({
      legendByCustom: legendByCustom.value,
      legendBy: legendBy.value,
      lineItems: cloneDeep(saveData)
    });
    const params: CustomLegendDataParams[] = saveData.map((item: any) => ({
      requestId: props.websocketParams.requestId,
      requestDt: props.websocketParams.requestDt,
      contextKey: item.contextKey,
      infoJson: {
        legendByCustom: legendByCustom.value,
        legendBy: legendBy.value,
        lineItems: [
          {
            contextKey: item.contextKey,
            customLegendName: item.customLegendName,
            color: item.color,
            isChecked: item.isChecked,
            ctxExtendedId: item?.ctxExtendedId,
            // overlay列表所需数据
            key: item.key,
            dataSet: item.dataSet,
            lotId: item.lotId,
            wafer: item.wafer,
            loopNo: item.loopNo
          }
        ]
      },
      ctxExtendedId: item?.ctxExtendedId
    }));
    const saveRes = await saveCustomLegendData({
      bodyParams: params
    });
    if (saveRes?.status == 'SUCCESS') {
      message.success(t('common.tip.saveSuccess'));
      // 改变图表
      emit('filterChart', lineItems.value);
      // 关闭弹窗
      handleCancel();
    } else {
      message.error(t('common.tip.saveError'));
    }
  }
  loading.value = false;
};
const getCustomLegendInitData = (legendBy: string) => {
  return props.data?.map((item: any) => {
    const startTime = moment(item.startDtts);
    const endTime = moment(item.endDtts);
    const diff = moment.duration(endTime.diff(startTime));
    const col = props.groupColumns.find((col) => col.id == item.groupConfigId);
    return {
      key: col?.groupNameAlias || col?.key,
      isChecked: true,
      // 定义的数据
      contextKey: item?.contextKey,
      // customLegendName默认就是dataset的内容
      customLegendName: item?.[legendBy] || col?.groupNameAlias || col?.key,
      color: col?.color,
      dataSet: item?.groupConfigId,
      eqpId: item?.eqpId,
      chamber: item?.chamber,
      lotId: item?.lotId,
      wafer: item?.substrateId?.split(';')[0],
      loopNo: item?.loopNo,
      recipeId: item?.recipeId,
      startTime: startTime.format(CHART_CONSTANT.TraceTimeFormat),
      endTime: endTime.format(CHART_CONSTANT.TraceTimeFormat),
      processDuration: moment.utc(diff.asMilliseconds()).format(CHART_CONSTANT.ProcessDuration),
      ctxExtendedId: item?.ctxExtendedId
    };
  });
};
const buildTableData = (initTableData: LineItem[], customLegendData: CustomLegendData) => {
  // 重置配置为默认值
  legendByCustom.value = customLegendData?.legendByCustom || false;
  legendBy.value = customLegendData?.legendBy || '';
  const newInitData = cloneDeep(initTableData);
  // 安全更新 store
  safeUpdateStore({
    legendByCustom: customLegendData?.legendByCustom || false,
    legendBy: customLegendData?.legendBy || '',
    lineItems: newInitData
  });
  lineItems.value = newInitData;
  lineItemsTable.value = newInitData;
  // 用于apply校验时使用的数据源
  lineItemsTableTemp.value = cloneDeep(initTableData);
};
const initCustomModel = (newData: any[]) => {
  // 检查 store 中是否有有效的配置数据
  const { customLegendData } = customLegendStore;
  // 初始化数据
  const initTableData = getCustomLegendInitData(customLegendData.legendBy);
  const hasValidStoreConfig =
    customLegendData?.lineItems?.length > 0 &&
    customLegendData.lineItems.some((storeItem: any) =>
      newData.some((dataItem: any) => dataItem.contextKey === storeItem.contextKey)
    );
  if (hasValidStoreConfig) {
    // 使用 store 中的配置数据，但确保数据与 props.data 完全一致
    legendByCustom.value = customLegendData.legendByCustom;
    legendBy.value = customLegendData.legendBy;
    // 合并数据：以 props.data 为准，保留 store 中的配置
    const mergedLineItems = initTableData.map((initItem: any) => {
      const storeItem = customLegendData.lineItems.find(
        (item: any) => item.contextKey === initItem.contextKey
      );
      if (storeItem) {
        // 如果 store 中有对应数据，保留用户的配置（颜色、自定义名称、选中状态等）
        return {
          ...initItem, // 基础数据以 props.data 为准
          customLegendName: storeItem.customLegendName || initItem.customLegendName,
          color: storeItem.color || initItem.color,
          isChecked: storeItem.isChecked !== undefined ? storeItem.isChecked : initItem.isChecked,
          ctxExtendedId: storeItem.ctxExtendedId || initItem.ctxExtendedId
        };
      } else {
        // 如果是新数据，使用默认配置
        return initItem;
      }
    });
    lineItemsTable.value = mergedLineItems;
    lineItemsTableTemp.value = cloneDeep(mergedLineItems);
    lineItems.value = mergedLineItems.filter((item: any) => item.isChecked);
    // 安全更新 store 以保持数据同步
    safeUpdateStore({
      legendByCustom: legendByCustom.value,
      legendBy: legendBy.value,
      lineItems: cloneDeep(mergedLineItems)
    });
  } else {
    // store 数据无效或不匹配，重新构建表格数据
    buildTableData(initTableData, customLegendData);
  }
};
// 监听 props.data 变化，重新构建数据
watch(
  () => props.data,
  (newData) => {
    if (!newData || newData.length === 0) return;
    initCustomModel(newData);
  },
  { immediate: true }
);
// 单独监听 store 变化，用于同步界面状态（不修改 store，避免循环）
watch(
  () => customLegendStore.customLegendData,
  (newCustomLegendData) => {
    if (isUpdatingStore.value) return;
    // 只同步界面状态，不修改数据
    if (newCustomLegendData?.lineItems?.length > 0) {
      legendByCustom.value = newCustomLegendData.legendByCustom;
      legendBy.value = newCustomLegendData.legendBy;
    }
  }
);
// 外层修改name和颜色时，需要同步到custom legend中
watch(
  [() => props.groupColumns],
  () => {
    lineItemsTable.value.forEach((item: any) => {
      const col = props.groupColumns.find((col) => col.id == item.dataSet);
      item.key = col.key;
    });
  },
  { immediate: true }
);
</script>

<template>
  <div class="custom-legend-warp">
    <EesButtonTip
      is-border
      :marginRight="10"
      icon="#icon-btn-custom-legend"
      type="svg"
      :text="t('cm.btn.customLegend')"
      @click="handleOpen"
    />
    <vxe-modal
      v-model="visible"
      class-name="full-modal sidebar-no-padding"
      :width="'90%'"
      :height="'90%'"
      :title="t('cm.btn.customLegend')"
      :destroy-on-close="true"
      :show-footer="true"
      resize
      show-zoom
      @ok="handleOk"
      @cancel="handleCancel()"
      :loading="loading"
    >
      <div class="filter-content-warp">
        <div class="custom-legend">
          <div class="custom-legend-config">
            <div class="show-legend-by-custom">
              <span class="label">{{ t('cm.title.showLegendByCustom') }}</span>
              <a-switch v-model:checked="legendByCustom" />
            </div>
            <div>
              <span class="label">{{ t('cm.title.legendBy') }}</span>
              <a-select
                v-model:value="legendBy"
                style="width: 200px"
                :options="getLegendBy()"
                allowClear
                @change="handleLegendByChange"
              />
            </div>
          </div>
          <div class="custom-legend-total">
            <p>
              {{ t('cm.title.select') }}: <span>{{ lineItems.length }}</span>
            </p>
            <p>
              {{ t('cm.title.total') }}: <span>{{ uniqueLineItemsTable.length }}</span>
            </p>
          </div>
        </div>
        <div class="content-table-warp">
          <vxe-grid
            :id="'custom-legend-grid'"
            ref="xGrid"
            v-bind="options"
            :data="uniqueLineItemsTable"
            :edit-rules="validRules"
            :edit-config="{
              trigger: 'dblclick',
              mode: 'cell',
              showStatus: false,
              showStatusBorder: true
            }"
            v-on="tableEvent"
          >
            <template #color="{ row }">
              <div class="color-select-wrapper">
                <ColorSelect
                  :showArrow="true"
                  :tooltipText="$t('cm.tips.colorOptions')"
                  :curColor="row?.color || ''"
                  @selectColor="uploadColor(row, $event)"
                />
              </div>
            </template>
          </vxe-grid>
        </div>
      </div>
      <template #footer>
        <div class="apply-box">
          <a-button key="submit" type="primary" @click="handleOk">Apply</a-button>
          <a-button key="back" class="close-btn" @click="handleCancel()">Close</a-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.custom-legend-warp {
  .filter-content-warp {
    display: flex;
    flex-direction: column;
    height: 100%;
    .custom-legend {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 8px 10px;
      &-config {
        display: flex;
        flex-direction: row;
        align-items: center;
        .show-legend-by-custom {
          margin-right: 8px;
        }
        .label {
          margin-right: 8px;
        }
      }
      &-total {
        display: flex;
        flex-direction: row;
        align-items: center;
        p {
          color: @text-hint-color;
          margin: 0 10px 0 0;
          span {
            color: @text-subtitle-color;
          }
        }
      }
    }
    .content-table-warp {
      height: calc(100% - 50px);
      .color-select-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  :deep(.vxe-modal--footer) {
    border: none;

    .close-btn {
      margin-left: 10px;
    }
  }
}
</style>
