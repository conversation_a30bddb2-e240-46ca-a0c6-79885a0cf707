import type { CustomLegendData } from "@futurefab/ui-sdk-stores";
import type { CmFilterObject } from "../interface";

export interface MetrologyOption {
  summaryAndWaferData: any[];
  customLegendItems: any[];
  filterInfo: CmFilterObject;
  chartType: 'Trend' | 'Scatter';
  summaryType: string;
  metrologyParams: string;
  isDeleteBlank: boolean;
  chartLegendConfig: any;
  groupColumns: any[];
  slopeByGroup: boolean;
  customLegendData: CustomLegendData;
}
export interface StatisticInfo {
  slope: number;
  intercept: number;
  rSquared: number;
  fValue: number;
  pValue: number;
  pSlope: number;
  name?: string;
}
export interface StatisticTemplateInfo extends StatisticInfo{
    zIndex: number;
    originLeft: number;
    show: boolean;
}