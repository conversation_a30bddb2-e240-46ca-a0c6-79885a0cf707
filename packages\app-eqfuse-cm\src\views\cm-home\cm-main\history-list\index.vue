<script lang="ts" setup>
import { EesButtonTip, <PERSON>esSidebar, EesCustomTime } from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';
import { getCmAnalysisHistories, deleteAnalysisHistory } from '@futurefab/ui-sdk-api';
import { ref, watch } from 'vue';
import { VXETable } from '@futurefab/vxe-table';
import dayjs from 'dayjs';
import HistoryLink from './history-link.vue';
import { apiConfig } from '@futurefab/ui-sdk-api';
import PopConfirm from '@/components/pop-confirm/index.vue';
import { message } from '@futurefab/ant-design-vue';

// 时间
const dates = ref<string[]>([]);

// 表格
const openHistory = ref<boolean>(false);
const historyLoading = ref<boolean>(false);
const xGird = ref();
const historyData = ref([]);
const options = VXETable.tableFun.tableDefaultConfig({
  columns: [
    {
      field: 'requestId',
      title: 'common.field.id',
      minWidth: 100,
      sortable: true,
      filterRender: { name: '$input' },
      filters: [{ data: '' }],
      slots: { default: 'historyIdSlot' }
    },
    {
      field: 'requestDt',
      title: 'common.field.createdTime',
      minWidth: 250,
      sortable: true,
      filterRender: { name: '$input' },
      filters: [{ data: '' }],
      slots: { default: 'historyTimeSlot' }
    },
    {
      field: 'action',
      title: 'cm.field.action',
      minWidth: 100,
      filterRender: {},
      slots: { default: 'historyActionSlot' }
    }
  ],
  mouseConfig: {
    selected: false,
    area: false
  }
});
const events = {};

// 请求
const currentPage = ref(1);
const pageSize = 100000;
const getAnalysisHistories = async () => {
  try {
    historyLoading.value = true;
    const res = await getCmAnalysisHistories({
      bodyParams: {
        page: currentPage.value,
        limit: pageSize,
        startTime: dates.value[0],
        endTime: dates.value[1]
      }
    });
    if (res.status === 'SUCCESS') {
      historyData.value = res.data;
      historyLoading.value = false;
    } else {
      historyLoading.value = false;
    }
  } catch (error) {
    historyLoading.value = false;
    console.error('获取历史分析记录失败:', error);
  }
};

const handleClick = () => {
  openHistory.value = false;
};

const refTime = ref();
// 当月日期
// const timeAreaBind = ref<any>([dayjs().startOf('month'), dayjs()]);
// 最近30天
const timeAreaBind = ref<any>([dayjs().subtract(30, 'day'), dayjs()]);
const timePop = (val: any) => {
  if (val?.length > 0) {
    dates.value = [
      dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'),
      dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss')
    ];
  }
};

// 删除历史记录
const handleDeleteAnalysisHistory = async (requestId: number, requestDt: string) => {
  const result = await deleteAnalysisHistory({
    bodyParams: {
      requestId,
      requestDt
    }
  });
  if (result.status === 'SUCCESS') {
    message.success(t('common.tip.delSuccess'));
    getAnalysisHistories();
  } else {
    message.error(result.msg || t('common.tip.deletionFailed'));
  }
};

watch(
  dates,
  (newVal) => {
    if (newVal.length === 2) {
      getAnalysisHistories();
    }
  },
  { immediate: true }
);
</script>

<template>
  <ees-button-tip
    is-border
    icon="#icon-btn-history"
    :text="t('common.btn.history')"
    @click="() => (openHistory = true)"
  />
  <EesSidebar
    :key="'analysisHistoryList'"
    v-if="openHistory"
    :title="$t('common.btn.history')"
    :has-zoom="true"
    :width="550"
    class="sidebar-no-padding history-sidebar"
    @clickbutton="handleClick"
  >
    <template #sidebar_content>
      <div class="history-search">
        <EesCustomTime
          ref="refTime"
          :time-area="timeAreaBind"
          :timeAreaMax="'0'"
          :max-period="null"
          :show-title="false"
          :api-config="apiConfig"
          :isColumn="false"
          :bordered="true"
          @time-pop="timePop"
        ></EesCustomTime>
      </div>
      <div
        class="history-list-table"
        v-isLoading="{
          isShow: historyLoading,
          hasButton: false,
          title: 'Loading...'
        }"
      >
        <vxe-grid ref="xGird" :data="historyData" v-bind="options" v-on="events">
          <template #historyIdSlot="{ row }">
            <HistoryLink :row="row" />
          </template>
          <template #historyTimeSlot="{ row }">
            {{ dayjs(new Date(row.requestDt)).format('YYYY-MM-DD HH:mm:ss.SSS') }}
          </template>
          <template #historyActionSlot="{ row }">
            <PopConfirm
              :title="t('common.btn.delete')"
              :content="t('common.tip.deleteTip')"
              :okText="t('common.btn.delete')"
              @ok="handleDeleteAnalysisHistory(row.requestId, row.requestDt)"
            >
              <template #popover-btn>
                <a-button type="link" size="small">{{ t('common.btn.delete') }}</a-button>
              </template>
            </PopConfirm>
          </template>
        </vxe-grid>
      </div>
    </template>
  </EesSidebar>
</template>

<style lang="less" scoped>
.history-sidebar {
  :deep(.ant-drawer-content-wrapper) {
    min-width: 550px !important;
  }

  .history-search {
    padding: 10px 14px;
  }

  .history-list-table {
    width: 100%;
    height: calc(100% - 52px);
  }
}
</style>
