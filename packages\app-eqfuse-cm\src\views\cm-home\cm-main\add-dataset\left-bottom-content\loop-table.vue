<script lang="ts" setup>
import { ref, watch, shallowRef, computed } from 'vue';
import {
  getTSVLoopWaferTableOptions,
  getLoopWaferTableOptions,
  getLoopTableOptions
} from '../config';
import type { ContextMap } from '../interface';
import { getHisWaferOfLoopList, getEesDbWaferInfoData } from '@futurefab/ui-sdk-api';
import { cloneDeep, debounce } from 'lodash-es';
import { cmMainInfo as buttonIds } from '@/log-config';
import CommonTitle from '@/views/cm-home/cm-main/add-dataset/common-title/index.vue';
import CommonGrid from '@/views/cm-home/cm-main/add-dataset/common-grid/index.vue';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { Splitpanes, Pane } from 'splitpanes';

const props = withDefaults(
  defineProps<{
    columns: ContextMap[];
    loopData: any[];
    groupStore?: any;
    runType?: string;
    specialKey?: string;
  }>(),
  {
    columns: () => [],
    loopData: () => [],
    groupStore: {},
    runType: 'CSV',
    specialKey: ''
  }
);
const groupStore = props.groupStore;
const checkData = defineModel<any>('checkData');
const uploadTable = defineModel<boolean>('uploadTable');
// 右键分组
const emits = defineEmits(['clickGrouping']);
const clickGrouping = (groupName: string, data: any) => {
  emits('clickGrouping', groupName, data);
};
// loop wafer table
const waferTableData: any = shallowRef([]);
const waferCheckData: any = shallowRef([]);
// select loop options
const loopGroup = ref('');
const loopGroupOptions: any = ref([]);
// loop table
const loopTableData = shallowRef([]);
const groupLoading = ref(false);
const handleLoopGroupChange = (value: string, allData: any) => {
  groupLoading.value = true;
  groupStore.handleClear();
  loopTableData.value = allData?.tableValue?.map((item: any) => ({
    wafer: item?.waferId?.split(';')?.[0],
    ...item,
    fileName: item?.waferId?.split(';')?.[1]
  }));
  // 设置 loop step range
  groupStore.handleLoopStep(value);
  groupStore.handleStepIds(allData?.stepIds);
  if (waferCheckData.value && waferCheckData.value.length > 0) {
    groupStore.handleCurrentWafer(...waferCheckData.value);
  }
  setTimeout(() => {
    groupLoading.value = false;
  }, 300);
};
const waferCommonGrid = ref();
const loopCommonGrid = ref();
const getData = () => {
  return loopCommonGrid.value?.getData();
};
const addTableData = (data: any[]) => {
  loopCommonGrid.value?.addTableData(data);
};
const getWaferTableDataLength = (): number => {
  return waferCommonGrid.value?.getData()?.visibleData?.length || 0;
  // return waferCommonGrid.value?.getData()?.tableData?.length || 0;
};
const getLoopTableDataLength = (): number => {
  return loopCommonGrid.value?.getData()?.visibleData?.length || 0;
};
// 更新缓存的数据
const reloadCacheData = async (row: any, type: string) => {
  // 选中之前选中的wafer
  setTimeout(() => {
    // 选中之前选中的wafer
    waferCommonGrid.value?.setCheckRow(row, type);
    // 还原loop
    groupStore.handleLoopStep(groupStore.loopStepTemp);
    groupStore.handleStepIds(groupStore?.stepIdsTemp);
    loopGroup.value = cloneDeep(groupStore.loopStep);
    // 给loop table赋值
    const allCheck: any[] = [];
    groupStore.backupsCheckData.forEach((value: any) => allCheck.push(...value));
    // Loop List Data
    const tempDeepArray = cloneDeep(groupStore.loopDataTemp);
    const cacheList = tempDeepArray?.filter(
      (item: any) => !allCheck?.find((obj: any) => item._X_ROW_KEY === obj._X_ROW_KEY)
    );
    loopTableData.value = cacheList;
    // 还原选中数据
    groupStore.checkData = cloneDeep(groupStore.backupsCheckData);
  });
};
const deleteTableData = (deleteRows?: any) => {
  loopCommonGrid.value.deleteTableData(deleteRows);
};
const verify = (value: ContextMap[]) => {
  return value && value.length > 0;
};
const clearSelectGroup = (tempArr?: any) => {
  if (tempArr && tempArr.length > 0) {
    loopGroupOptions.value = tempArr;
  } else {
    // 手动清空select
    loopGroup.value = '';
    loopGroupOptions.value = [];
    handleLoopGroupChange('', { tableValue: [] });
  }
};
const sortLoopItems = (array: any) => {
  return array?.sort((a: any, b: any) => Number(a['LOOP_NO']) - Number(b['LOOP_NO']));
};
const setSelectGroup = async (row?: any) => {
  groupLoading.value = true;
  const tempArr: any = [];
  // 手动清空select
  clearSelectGroup();
  if (props.runType === 'HIS') {
    if (waferCheckData.value.length === 0) return;
    const checkLine = waferCheckData.value?.map((object: any) => {
      const { _X_ROW_KEY, ...rest } = object;
      return rest;
    });
    const allGroupInfo = await getHisWaferOfLoopList({
      bodyParams: checkLine[0],
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-his-wafer-of-loop-list'
      }
    });
    if (allGroupInfo.status === 'SUCCESS' && allGroupInfo.data?.length > 0) {
      // 所有选中wafer的group数据
      allGroupInfo.data.forEach(({ name, loopItems, stepIds }: any) => {
        tempArr.push({
          label: name,
          value: name,
          tableValue: sortLoopItems(loopItems),
          stepIds
        });
      });
    }
    clearSelectGroup(tempArr);
  } else if (props.runType === 'DATABASE') {
    if (waferCheckData.value.length === 0) return;
    const allGroupInfo = await getEesDbWaferInfoData({
      bodyParams: waferCheckData.value[0],
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-ees-db-wafer-info-data'
      }
    });
    if (allGroupInfo.status === 'SUCCESS' && allGroupInfo.data?.length > 0) {
      // 所有选中wafer的group数据
      allGroupInfo.data.forEach(({ name, loopItems, stepIds }: any) => {
        tempArr.push({
          label: name,
          value: name,
          tableValue: sortLoopItems(loopItems),
          stepIds
        });
      });
    }
    clearSelectGroup(tempArr);
  } else {
    if (waferCheckData.value?.length > 0) {
      waferCheckData.value.forEach(({ stepGroups }: any) => {
        // 所有选中wafer的group数据
        stepGroups?.forEach(({ name, loopItems, stepIds }: any) => {
          tempArr.push({
            label: name,
            value: name,
            tableValue: sortLoopItems(loopItems),
            stepIds
          });
        });
      });
    }
    clearSelectGroup(tempArr);
  }
  setTimeout(() => {
    groupLoading.value = false;
  }, 300);
};
const waferWarpRef = ref();
const loopWarpRef = ref();
const setColumnFieldList = () => {
  return props.columns?.map((item: any) =>
    props.specialKey ? (item?.key === 'waferId' ? props.specialKey : item?.key) : item?.key
  );
};
// 编辑事件
const editChangeEvents = debounce((data: any) => {
  const { row, column } = data;
  // console.log(`xxxxx ${column.title} 触发 input change 事件`, data, row[column.field]);
  // 修改row.stepGroups内所有对象数组中的loopItems的column.field字段值为row[column.field]的值
  row.stepGroups?.forEach((item: any) => {
    item.loopItems?.forEach((loopItem: any) => {
      loopItem[column.field] = row[column.field];
    });
  });
  // 更新Loop List
  if (loopGroup.value) {
    waferCheckData.value = [row];
    const allData = row.stepGroups.find((item: any) => item.name === loopGroup.value);
    handleLoopGroupChange(loopGroup.value, {
      ...allData,
      tableValue: allData?.loopItems
    });
  }
}, 100);
// options
const waferOptions = computed(() => {
  return props.runType === 'TSV'
    ? getTSVLoopWaferTableOptions(
        props.columns?.filter((item: any) => item.key !== 'loopNo'),
        props.specialKey,
        editChangeEvents
      )
    : getLoopWaferTableOptions(
        props.columns?.filter((item: any) => item.key !== 'loopNo'),
        props.specialKey
      );
});
const reloadFlag = ref<boolean>(false);
const loopOptions = computed(() => {
  return getLoopTableOptions(props.columns, props.specialKey, reloadFlag.value);
});
// 更新右键菜单内容
const reloadTableOptions = () => {
  reloadFlag.value = !reloadFlag.value;
};
watch(
  () => props.loopData,
  () => {
    // 初始化
    waferTableData.value = props.loopData;
    waferCheckData.value = [];
    loopTableData.value = [];
    clearSelectGroup();
  },
  { immediate: true }
);

defineExpose({ getData, addTableData, deleteTableData, reloadCacheData, reloadTableOptions });
</script>

<template>
  <splitpanes class="default-theme wafer-loop-warp" horizontal>
    <pane
      size="50"
      class="pane-item bottom-border"
      v-isLoading="{
        isShow: groupLoading,
        hasButton: false,
        title: 'Loading...'
      }"
    >
      <div ref="waferWarpRef" :class="'wafer-loop-warp-container'">
        <CommonTitle
          v-model:box-ref="waferWarpRef"
          :title="$t('cm.title.dataList')"
          :count="getWaferTableDataLength()"
          :border="false"
        >
          <template #left-icon>
            <span class="text-icon">
              <a-tooltip placement="bottom">
                <i class="iconfont chart_icon_icon zoom-icon icon-btn-help" />
                <template #title>
                  {{ $t('cm.label.datasetListTip') }}
                </template>
              </a-tooltip>
            </span>
          </template>
        </CommonTitle>
        <div class="wafer-table-grid">
          <CommonGrid
            v-if="verify(columns) && uploadTable"
            :id="'wafer-table-grid'"
            ref="waferCommonGrid"
            v-model:check-data="waferCheckData"
            :options="waferOptions"
            :data="waferTableData"
            :column-field-list="setColumnFieldList()"
            :type="'radio'"
            :run-type="runType"
            :clickRowToSelect="false"
            @special-handle="setSelectGroup"
          />
          <NoData v-else :border="false" :special-border="false" />
        </div>
      </div>
    </pane>
    <pane
      size="50"
      class="pane-item top-border"
      v-isLoading="{
        isShow: groupLoading,
        hasButton: false,
        title: 'Loading...'
      }"
    >
      <div ref="loopWarpRef" class="wafer-loop-warp-container">
        <CommonTitle
          v-model:box-ref="loopWarpRef"
          :title="$t('cm.title.loopList')"
          :count="getLoopTableDataLength()"
          :border="false"
        >
          <template #left-content>
            <span class="loop-group-select">
              <span class="label">Loop Group</span>
              <a-select
                v-model:value="loopGroup"
                allow-clear
                style="width: 180px"
                :options="loopGroupOptions"
                :show-arrow="true"
                @change="handleLoopGroupChange"
              />
            </span>
          </template>
        </CommonTitle>
        <div class="loop-table-grid">
          <CommonGrid
            v-if="verify(columns) && uploadTable"
            :id="'loop-table-grid'"
            ref="loopCommonGrid"
            v-model:check-data="checkData"
            :options="loopOptions"
            :data="loopTableData"
            :column-field-list="setColumnFieldList()"
            @clickGrouping="clickGrouping"
          />
          <NoData v-else :border="false" :special-border="false" />
        </div>
      </div>
    </pane>
  </splitpanes>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.wafer-loop-warp {
  height: 100%;
  overflow: scroll;
  // 显示滚动条
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: @bg-scroll-color;
    }
  }
  &::-webkit-scrollbar-corner {
    background-color: @bg-scroll-color;
  }
  .pane-item {
    min-height: 260px;
  }
  .bottom-border {
    border-bottom: 1px solid @border-color;
  }
  .top-border {
    border-top: 1px solid @border-color;
  }
  &-container {
    background-color: @bg-block-color;
    border-radius: 0 0 4px 4px;
    height: 100%;
    .text-icon {
      color: @text-weak-text;
      margin-right: 4px;
      :hover {
        color: @primary-color;
      }
    }
  }
  .wafer-table-grid {
    height: calc(100% - 38px);
  }
  .loop-group-select {
    margin-left: 10px;
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .label {
      font-weight: bold;
      color: @text-sub-color;
      margin: 0 10px 0 0;
    }
  }
  .loop-table-grid {
    height: calc(100% - 50px);
  }
}
</style>
