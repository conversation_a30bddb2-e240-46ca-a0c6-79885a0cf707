import { VXETable } from '@futurefab/vxe-table';
import type { ContextMap, MetaInputObject, MetaResult } from './interface';
import { DATA_GROUP } from '@/constant/charts';
import { useChartColor } from '@/utils';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import moment from 'moment';

// 针对分组名称的补充
export const getReplenishGroup = () => {
  const replenishGroupCache =
    localStorage
      .getItem('REPLENISH_GROUP')
      ?.split(',')
      ?.map((item: string) => {
        return { label: item, value: item };
      }) || [];
  return [...replenishGroupCache, ...DATA_GROUP];
};

export const groupMenuConfig = (
  disabledGroup: string[],
  title: string = 'Add ' + datasetGroupStore.groupTitle
) => {
  const groupMenuTemp = getReplenishGroup().map((item: { label: string; value: string }) => ({
    code: item.value,
    name: datasetGroupStore.groupTitle + ' ' + item.label,
    visible: true,
    disabled: disabledGroup.includes(item.value),
    className: 'eq-fuse-group-table-menu-item'
    // suffixIcon: 'iconfont icon-close'
  }));
  const groupMenu = [
    {
      code: title,
      name: title,
      visible: true,
      disabled: false,
      className: 'eq-fuse-group-table-menu-title'
    },
    ...groupMenuTemp
  ];
  return {
    header: {},
    body: {
      options: [groupMenu]
    },
    footer: {},
    className: 'eq-fuse-group-table-menu'
  };
};
/**
 * wafer options
 * @param columns
 * @returns options
 */
export const getWaferOptions = (
  columns: ContextMap[],
  metrologyCols: string[],
  viewType?: string,
  specialKey?: string
) => {
  const isWafer = viewType === 'wafer';
  const isTime = (key: string) => {
    return key.toLocaleLowerCase().includes('time') || key.toLocaleLowerCase().includes('date');
  };
  const options: any =
    columns?.map((item) => {
      const slots =
        isWafer && !isTime(item?.key)
          ? {
              header: 'autoGroupHearder'
            }
          : {};
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const isStartTimeOrEndTime = ['fileName', 'StartTime', 'EndTime'].includes(item?.key);
      return {
        field,
        title: item?.name,
        minWidth: isStartTimeOrEndTime ? 200 : 150,
        sortable: true,
        filters: [],
        filterRender: {},
        slots
      };
    }) || [];
  const optionResult = VXETable.tableFun.tableDefaultConfig({
    mouseConfig: {
      selected: true,
      area: true
    },
    columns: [
      {
        type: 'checkbox'
      },
      ...options
    ],
    // 关闭虚拟滚动，解决数据量大的时候右键无法获取框选信息的问题
    // scrollY: {
    //   enabled: false
    // },
    menuConfig: groupMenuConfig([])
  });
  // 删除tableDefaultConfig带入的三个右键选项
  optionResult.menuConfig.body.options[0].splice(-3);
  // optionResult.columns.splice(
  //   5,
  //   0,
  //   ...metrologyCols.map((item) => ({
  //     title: item.slice(0, -'-metrology'.length),
  //     field: item,
  //     className: 'add-metrology-cell',
  //     width: '150px',
  //     sortable: true
  //   }))
  // );
  return optionResult;
};
// loop wafer options
export const getLoopWaferTableOptions = (columns: ContextMap[], specialKey: string) => {
  const options: any =
    columns?.map((item) => {
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const isStartTimeOrEndTime = ['StartTime', 'EndTime'].includes(item?.key);
      return {
        field,
        title: item?.name,
        minWidth: isStartTimeOrEndTime ? 200 : 150,
        sortable: true,
        filters: [],
        filterRender: {}
      };
    }) || [];
  return VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'radio'
      },
      ...options
    ],
    mouseConfig: {
      selected: true,
      area: true
    }
  });
};
// loop options
export const getLoopTableOptions = (
  columns: ContextMap[],
  specialKey: string,
  reloadFlag?: boolean
) => {
  const tempOptions: any =
    columns?.map((item) => {
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const textIsLong = ['fileName', 'StartTime', 'EndTime'].includes(item?.key);
      return {
        field,
        title: item?.name,
        minWidth: textIsLong ? 200 : 150,
        sortable: true,
        filters: [],
        filterRender: {}
      };
    }) || [];
  const options = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'checkbox'
      },
      ...tempOptions
    ],
    mouseConfig: {
      selected: true,
      area: true
    },
    // 关闭虚拟滚动，解决数据量大的时候右键无法获取框选信息的问题
    // scrollY: {
    //   enabled: false
    // },
    menuConfig: groupMenuConfig([])
  });
  // 删除tableDefaultConfig带入的三个右键选项
  options.menuConfig.body.options[0].splice(-3);
  // options.columns.shift();
  return options;
};
// group data select
export const getGroupTableOptions = (
  columns: ContextMap[],
  specialKey: string,
  disabledGroup: string[],
  allMetrologyCols: string[]
) => {
  // const metrologyColumns = allMetrologyCols.map((item) => ({
  //   title: item.slice(0, -'-metrology'.length),
  //   field: item,
  //   className: 'add-metrology-cell',
  //   width: '150px',
  //   sortable: true
  // }));
  const tempOptions: any =
    columns?.map((item) => {
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const isStartTimeOrEndTime = ['StartTime', 'EndTime'].includes(item?.key);
      return {
        field,
        title: item.name,
        minWidth: isStartTimeOrEndTime ? 200 : 150,
        sortable: true,
        filters: [],
        filterRender: {}
      };
    }) || [];
  const options = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'checkbox'
      },
      // ...metrologyColumns,
      ...tempOptions
    ],
    mouseConfig: {
      selected: true,
      area: true
    },
    menuConfig: groupMenuConfig(disabledGroup, 'Move ' + datasetGroupStore.groupTitle)
  });
  // 删除tableDefaultConfig带入的三个右键选项
  options.menuConfig.body.options[0].splice(-3);
  options.columns.shift();
  return options;
};
/**
 * TSV wafer options/TSV waferLoop options
 * @param columns
 * @returns options
 */
export const getTSVWaferOptions = (
  columns: ContextMap[],
  metrologyCols: string[],
  viewType?: string,
  specialKey?: string
) => {
  const isWafer = viewType === 'wafer';
  const isTime = (key: string) => {
    return key.toLocaleLowerCase().includes('time') || key.toLocaleLowerCase().includes('date');
  };
  const options: any =
    columns?.map((item) => {
      const slots =
        isWafer && !isTime(item?.key)
          ? {
              header: 'autoGroupHearder'
            }
          : {};
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const textIsLong = ['fileName', specialKey, 'waferId', 'StartTime', 'EndTime'].includes(
        item?.key
      );
      if (textIsLong) {
        return {
          field,
          title: item?.name,
          minWidth: 200,
          sortable: true,
          filters: [],
          filterRender: {},
          slots
        };
      }
      // 支持编辑
      return {
        field,
        title: item?.name,
        minWidth: 170,
        sortable: true,
        filters: [],
        filterRender: {},
        editRender: { name: '$input' },
        slots
      };
    }) || [];
  const optionResult = VXETable.tableFun.tableDefaultConfig({
    mouseConfig: {
      selected: true,
      area: true
    },
    editConfig: {
      trigger: 'dblclick',
      mode: 'cell'
    },
    columns: [
      {
        type: 'checkbox'
      },
      ...options
    ],
    // 关闭虚拟滚动，解决数据量大的时候右键无法获取框选信息的问题
    // scrollY: {
    //   enabled: false
    // },
    menuConfig: groupMenuConfig([])
  });
  // 删除tableDefaultConfig带入的三个右键选项
  optionResult.menuConfig.body.options[0].splice(-3);
  // optionResult.columns.splice(
  //   5,
  //   0,
  //   ...metrologyCols.map((item) => ({
  //     title: item.slice(0, -'-metrology'.length),
  //     field: item,
  //     className: 'add-metrology-cell',
  //     width: '150px',
  //     sortable: true
  //   }))
  // );
  return optionResult;
};
export const getTSVLoopWaferTableOptions = (
  columns: ContextMap[],
  specialKey: string,
  editChangeEvents: any
) => {
  const options: any =
    columns?.map((item) => {
      const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const textIsLong = ['fileName', specialKey, 'waferId', 'StartTime', 'EndTime'].includes(
        item?.key
      );
      if (textIsLong) {
        return {
          field,
          title: item?.name,
          minWidth: 200,
          sortable: true,
          filters: [],
          filterRender: {}
        };
      }
      // 支持编辑
      return {
        field,
        title: item?.name,
        minWidth: 170,
        sortable: true,
        filters: [],
        filterRender: {},
        editRender: { name: '$input', events: { change: editChangeEvents } }
      };
    }) || [];
  return VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'radio'
      },
      ...options
    ],
    editConfig: {
      trigger: 'dblclick',
      mode: 'cell'
    },
    mouseConfig: {
      selected: true,
      area: true
    }
  });
};
/**
 * 获取csvMate下的key
 * 首页Count展示的字段
 * wafer: lot wafer
 * loop: lot wafer loop
 * station: lot wafer
 * @param csvMate
 * @returns columns
 */
export const getMetaColumns = (
  csvMeta: any,
  [key]: string[],
  isLoop?: boolean,
  specialKey?: string
) => {
  const countColumns = isLoop ? ['lotId', 'waferId', 'loopNo'] : ['lotId', 'waferId'];
  const getColumnInfo = (k: string, obj: any) => ({
    key: specialKey ? (k === 'waferId' ? specialKey : k) : k,
    name: obj.name || k,
    loopStep: obj?.loopStep
  });

  return Object.entries(csvMeta?.[key] || {})
    .filter(([key, value]: [string, any]) => countColumns.includes(key))
    .map(([k, v]: [string, any]) => getColumnInfo(k, v));
};
export const getAllMetaColumns = (inputObject: MetaInputObject, keys: string[]): MetaResult[] => {
  const result: MetaResult[] = [];
  keys.forEach((key) => {
    const map = inputObject[key as keyof MetaInputObject];
    if (map) {
      for (const [mapKey, value] of Object.entries(map)) {
        result.push({
          key: mapKey,
          name: value.name,
          loop: value.loop,
          loopStep: value.loopStep
        });
      }
    }
  });
  return result;
};

// 处理复制组的数据
export const handleCopyData = (
  type: string,
  copyGroup: string,
  dataValue: any,
  groupStore: any,
  groupName?: string
) => {
  if (groupStore.checkData.has(groupName)) {
    for (const [key, value] of groupStore.checkData) {
      if (groupStore.copySet.has(key) && groupStore.copySet.get(key) === copyGroup) {
        const arr = value;
        if (type === 'add') {
          // 将 values 中的新元素添加到 arr 中
          dataValue.forEach((value: any) => {
            if (!arr.includes(value)) {
              arr.push(value);
            }
          });
          // 更新 Map 中的数组
          groupStore.checkData.set(key, arr);
        } else if (type === 'delete') {
          // 创建一个新的数组来保存删除后的结果
          const filteredArr = arr.filter((item: any) => !dataValue.includes(item));
          // 如果过滤后的数组长度发生了变化，则更新 Map 中的数组
          if (filteredArr.length !== arr.length) {
            groupStore.checkData.set(key, filteredArr);
          }
        }
      }
    }
  }
};
/**
 * 根据 context 去重，保留最新（数组中靠后）的项
 * @param {Array<Object>} data - 原始数据数组
 * @param {Array<string>} excludeFields - 需要排除的字段，默认为 ['StartTime', 'EndTime']
 * @returns {Array<Object>} 去重后的新数组
 */
export const deduplicateByContext = (
  data: any[],
  excludeFields = ['_X_ROW_KEY', 'StartTime', 'EndTime']
) => {
  const map = new Map();
  for (const item of data) {
    // 获取除 excludeFields 外的所有字段，并按字段名排序后拼接成 context 字符串
    const contextKeys = Object.keys(item)
      .filter((key) => !excludeFields.includes(key))
      .sort();
    const context = contextKeys.map((key) => `${key}:${item[key]}`).join(',');
    // 使用 context 作为 key，item 作为 value
    // 后面的 item 会覆盖前面的，实现“保留最新”
    map.set(context, item);
  }
  // 返回去重后的值数组
  return Array.from(map.values());
};
/**
 * 根据 context 去除重复数据，只要两条数据相同，就同时删除这两条数据
 * @param {Array<Object>} data - 原始数据数组
 * @param {Array<string>} excludeFields - 需要排除的字段，默认为 ['StartTime', 'EndTime']
 * @returns {Array<Object>} 删除重复数据后的新数组
 */
export const removeDuplicateByContext = (
  data: any[],
  excludeFields = ['_X_ROW_KEY', 'StartTime', 'EndTime']
) => {
  const map = new Map();
  for (const item of data) {
    // 获取除 excludeFields 外的所有字段，并按字段名排序后拼接成 context 字符串
    const contextKeys = Object.keys(item)
      .filter((key) => !excludeFields.includes(key))
      .sort();
    const context = contextKeys.map((key) => `${key}:${item[key]}`).join(',');
    // 如果 context 已经存在，则删除当前项
    if (map.has(context)) {
      map.delete(context);
    } else {
      // 否则，将 context 作为 key，item 作为 value
      map.set(context, item);
    }
  }
  // 返回去重后的值数组
  return Array.from(map.values());
};
export interface DealGroupConfig {
  checkMap: Map<string, any[]>;
  commonGroupConfig: any;
  runType: string;
  fileNames: any;
  runByModel: string;
  csvMeta: any;
}
// 设置loop模式下的waferLoops
export const setWaferLoopsParams = (selectRows: any) => {
  let waferLoops: any = {
    loopIds: [],
    wafer: {}
  };
  selectRows.forEach((row: any, index: number) => {
    waferLoops.loopIds.push(row.loopNo);
    if (index === 0) waferLoops.wafer = row;
  });
  waferLoops.loopIds = [...new Set(waferLoops.loopIds)];
  return waferLoops;
};
// 处理分组信息内容
export const dealGroupConfig = ({
  checkMap,
  commonGroupConfig,
  runType,
  fileNames,
  runByModel,
  csvMeta
}: DealGroupConfig) => {
  const fileNameList = new Set();
  const isLoop = runByModel === 'loop';
  const allContextMap: MetaResult[] = getAllMetaColumns(csvMeta, ['columnMap']);
  const contextMap: { key: string; name: any }[] = getMetaColumns(csvMeta, ['columnMap'], isLoop);
  commonGroupConfig!.setContextMap(contextMap);
  commonGroupConfig!.setAllContextMap(allContextMap);

  commonGroupConfig!.setLoop(isLoop);
  commonGroupConfig!.setRunType(runType);
  commonGroupConfig!.setStationCompare(runByModel === 'station');
  const groupConfigs = [];
  let count = 0;
  for (const [key, value] of checkMap) {
    count++;
    const groupConfig = commonGroupConfig!.deepCopy();
    groupConfig.reference = datasetGroupStore.referenceGroup.has(key);
    groupConfig.flag = `${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    groupConfig.sortId = commonGroupConfig!.getNewGroupConfigId(groupConfigs); // 设置sortId
    groupConfig.id = count;
    // groupConfig.id = DATA_GROUP.findIndex((item: any) => item.label === key) + 1;
    groupConfig.key = key;
    groupConfig.name = key;
    // Dynamically create Set variables based on contextMap keys
    const dynamicSets: { [key: string]: Set<any> } = {};
    contextMap.forEach((item: any) => {
      dynamicSets[item.name] = new Set();
    });
    const allDynamicSets: { [key: string]: Set<any> } = {};
    allContextMap.forEach((item: any) => {
      allDynamicSets[item.name] = new Set();
    });
    let from = '';
    let to = '';
    const context: any = [];
    let waferLoops: any = {
      stepIds: datasetGroupStore.stepIds,
      wafer: datasetGroupStore.currentWafer,
      loopIds: []
    };
    const eqpModuleIdsVal = new Set();
    value?.forEach((data: any, index: number) => {
      contextMap.forEach((item: any) => {
        if (item.name === 'Wafer') {
          dynamicSets[item.name].add(data['lotId'] + data[item.key]);
        } else {
          dynamicSets[item.name].add(data[item.key]);
        }
      });
      allContextMap.forEach((item: any) => {
        if (item.name === 'Wafer') {
          allDynamicSets[item.name].add(data['lotId'] + data[item.key]);
        } else {
          allDynamicSets[item.name].add(data[item.key]);
        }
      });
      // 起始时间
      if (from && data.StartTime) {
        from = moment(from).isBefore(moment(data.StartTime)) ? from : data.StartTime;
      } else {
        from = data.StartTime;
      }
      // 结束时间
      if (to && data.EndTime) {
        to = moment(to).isAfter(moment(data.EndTime)) ? to : data.EndTime;
      } else {
        to = data.EndTime;
      }
      // 非历史状态下的loop参数
      if (!['HISTORY'].includes(runType) && isLoop) waferLoops.loopIds.push(data['loopNo']);
      // 处理不同模式的特殊逻辑
      if (['CSV', 'TSV'].includes(runType)) fileNameList.add(data.waferId?.split(';')?.[1]);
      if (['DATABASE'].includes(runType)) eqpModuleIdsVal.add(data?.eqpModuleId);
      // 填充context
      context.push(data);
    });
    // 设定loop模式需要的特殊参数
    if (isLoop) {
      if (runType === 'HISTORY') {
        groupConfig.waferLoops = setWaferLoopsParams(value);
      } else {
        groupConfig.waferLoops = waferLoops;
      }
    }
    // Add dynamic variables to groupConfig
    const headerInfo: any = {};
    allContextMap.forEach((item: any) => {
      groupConfig[item.name] = Array.from(allDynamicSets[item.name]).join(',');
      headerInfo[item.name] = groupConfig[item.name];
    });
    groupConfig.loopGroup = datasetGroupStore.loopStep;
    groupConfig.from = moment(from);
    groupConfig.to = moment(to);
    groupConfig.selectedRows = context;
    // Set dynamic counts
    groupConfig.setDynamicCounts(dynamicSets);
    groupConfig.setHeaderInfo(
      Object.assign(
        headerInfo,
        isLoop
          ? {
              From: moment(from),
              To: moment(to),
              Count: groupConfig.count,
              'Loop Group': groupConfig.loopGroup
            }
          : {
              From: moment(from),
              To: moment(to),
              Count: groupConfig.count
            }
      )
    );
    // Set eqpModuleIds routesIds recipeId productIds
    if (['DATABASE'].includes(runType))
      groupConfig.setOneEqpModuleIdsVal([...eqpModuleIdsVal], groupConfig?.Recipe);
    groupConfig.setFileNecessaryAttributeConfigs(commonGroupConfig);
    groupConfigs.push(groupConfig);
  }
  // 分配颜色
  if (groupConfigs && groupConfigs.length > 0) useChartColor(groupConfigs);
  // CSV TSV模式下需要传递文件名信息
  if (['CSV', 'TSV'].includes(runType)) {
    commonGroupConfig!.setFileNames([...fileNameList]);
  } else {
    commonGroupConfig!.setFileNames(fileNames);
  }
  // 把这个新对象放进去
  commonGroupConfig!.addGroupConfig(groupConfigs);
};
// 处理分组信息内容
export const handleGroupData = (
  emits: any,
  commonGroupConfig: any,
  runByModel: string,
  waferData: any,
  fileNames: any,
  runType: string,
  handleCancel: any
) => {
  emits('confirm', 'delete', true);

  dealGroupConfig({
    checkMap: datasetGroupStore.checkData as any,
    commonGroupConfig,
    runByModel,
    runType,
    fileNames,
    csvMeta: waferData?.csvMeta
  });

  handleCancel();
  emits('renderNewGroupConfigs', commonGroupConfig);
};
