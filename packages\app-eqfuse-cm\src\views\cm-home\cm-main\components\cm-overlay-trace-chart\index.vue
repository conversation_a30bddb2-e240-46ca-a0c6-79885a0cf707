<script lang="ts" setup>
import {
  ref,
  watch,
  shallowRef,
  readonly,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  useTemplateRef,
  reactive
} from 'vue';
import SpecialTag from '@/views/cm-home/cm-main/components/special-tag/index.vue';
import OverlayTraceTable from './overlay-trace-table/index.vue';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import OverlayTraceChart from './overlay-trace-chart/index.vue';
import {
  showWarning,
  showError,
  EesButtonTip,
  useSelfFullScreen
} from '@futurefab/ui-sdk-ees-basic';
import { cloneDeep } from 'lodash-es';
import { groupWebSocketData } from '@/utils';
import { t } from '@futurefab/vxe-table';
import { useParameterColor } from '@/utils';
import EqFuseSidebar from '@/components/eqfuse-sidebar/index.vue';
import type { OverylayType } from './interface';
import { TreeSelect } from '@futurefab/ant-design-vue';
import {
  useBaseStore,
  useCmChartLegendStore,
  useCustomLegendStore,
  type LineItem
} from '@futurefab/ui-sdk-stores';
import { TRACE_CHART_X_AXIS_TYPE } from '@/constant/charts';
import GroupCustomConfigNew from '../../../cm-main/components/group-custom-configuration/group-custom-new.vue';
import CmChartCopy from '@/components/cm-chart-copy/index.vue';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import { Splitpanes, Pane } from 'splitpanes';
import { formatToXChart } from '@/utils';
import { getTraceOptions, setNormalization } from '../cm-result-chart/trace-chart/config';
import { useChartDomain } from '@futurefab/ui-sdk-ees-charts';
import type { CmFilterObject } from '../cm-result-chart/interface';
import { XChart, XChartVue } from '@xchart/vue';
import { useGroupLegend } from '../cm-result-chart/useGroupLegend';
import OverView from './components/OverView.vue';
import { snapdom } from '@zumer/snapdom';
import { useWindowSize } from '@vueuse/core';
import { debounce } from 'lodash-es';
import { getOverlayTraceChartMaxLimitParameter } from '@futurefab/ui-sdk-api';
import { EesAutoY, EesAutoMultiY } from '@futurefab/ui-sdk-ees-charts';
import CustomLegend from '../cm-result-chart/components/custom-legend/index.vue';

const chartConfigStore = useCmChartLegendStore();
const baseStore = useBaseStore();
const customLegendStore = useCustomLegendStore();

interface Props {
  tableData: any;
  commonGroupConfigVO: GroupConfigVO;
  websocketParams: any;
  treeData: any;
  waferData: any[];
  tableColumns: any[];
  isHistory: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  tableData: [],
  commonGroupConfigVO: () => new GroupConfigVO(),
  websocketParams: {},
  treeData: [],
  waferData: () => [],
  tableColumns: () => [],
  isHistory: false
});

// const overlayTableWaferData = computed(() => {
//   if (props.isHistory) {
//     return props.waferData;
//   } else {
//     if (props.tableColumns?.length <= 1) {
//       return [];
//     }
//     return props.tableColumns.slice(1).map((item: any) => ({
//       groupId: item.id,
//       groupName: item.key,
//       selectedRows: item.selectedRows
//     }));
//   }
// });
const emits = defineEmits(['handleSendSocket', 'changeColumn']);
const overlayTraceChartMaxLimitParameter = ref<number>(50);
onMounted(async () => {
  const res = await getOverlayTraceChartMaxLimitParameter({ bodyParams: {} });
  if (res.status === 'SUCCESS') {
    overlayTraceChartMaxLimitParameter.value = res.data;
  }
});

const getChildColor = (parameterName: string): string => {
  for (const group of treeData.value) {
    if (group.children) {
      const child = group.children?.find(
        (child: any) => child.parameterName?.toString() === parameterName
      );
      if (child && child.categoryColor) {
        return child.categoryColor;
      }
    }
  }

  return '';
};

const findRawGroupByKey = (key: number): any => {
  const row = treeData.value.find((item: any) => item.treeKey === key);
  return row;
};

const cellClassName = (isGroup: boolean, allColInfo: any) => {
  if (!isGroup) {
    let hasWarning = false;
    for (const [groupConfigId, value] of allColInfo) {
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return 'un-match-recipe'; // 返回错误样式类名
      }
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    if (hasWarning) return 'warning-recipe'; // 返回警告样式类名
    return ''; // 没有匹配到状态时返回空字符串
  }
  return ''; // 父节点返回空字符串
};

// 获取节点类名
const getNodeClassName = (parameterName: string, groupId: any) => {
  if (groupId) {
    // 子节点
    for (const group of treeData.value) {
      if (group.children) {
        const child = group.children?.find(
          (child: any) => child.parameterName?.toString() === parameterName
        );
        if (child) {
          const allColInfo = child.allColInfo || new Map();
          const cellClass = cellClassName(false, allColInfo);
          const className = cellClass ? `tree-child-node ${cellClass}`.trim() : 'tree-child-node';
          return className;
        }
      }
    }
    return 'tree-child-node';
  } else {
    // 父节点
    return 'tree-parent-node';
  }
};

const setSpecialTag = (row: any) => {
  // 检查 row.results 是否存在
  if (!row?.results || !Array.isArray(row.results)) {
    return { type: 'success', text: t('cm.label.matching') };
  }
  // 循环 row.results
  for (const result of row.results) {
    // 检查 allColInfo 是否存在且为 Map 结构
    if (!result?.allColInfo || !(result.allColInfo instanceof Map)) {
      continue;
    }
    // 循环 allColInfo Map，先收集所有状态
    let hasWarning = false;
    for (const [groupConfigId, value] of result.allColInfo) {
      // 检查 unmatchingRecipeSteps 长度大于0，直接返回 error
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return { type: 'error', text: t('cm.label.critical') };
      }
      // 检查 warningRecipeSteps 长度大于0，标记有警告
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    // 如果有警告，返回 warning
    if (hasWarning) return { type: 'warning', text: t('cm.label.warning') };
  }

  return { type: 'success', text: t('cm.label.matching') };
};

// ==================== 窗口大小相关 ====================

const { width: windowWidth } = useWindowSize();
const sidebarWidth = computed(() => {
  const width = windowWidth.value;

  if (width >= 1920) return 1600;
  if (width > 1620) return 1200;
  if (width > 1200) return width - 20;

  return Math.min(width * 0.85, width - 20);
});

// ==================== 基础状态管理 ====================
const showModel = ref(false);
const loading = ref(false);

watch(showModel, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      createIntersectionObserver();
      setTimeout(() => {
        observeChartElements();
      }, 100);
    }, 200);
  } else {
    disconnectIntersectionObserver();
  }
});

// ==================== Splitpanes 布局管理 ====================
const chartHeight = 600; // 每个图表的高度

// 计算显示行数，并将其转为{rowIndex：chartList}格式
const newChartList = computed(() => {
  const chartMap: Record<string, any[]> = {};
  let rowIndex = 1;
  selectedPamaterNames.value.forEach((paramName: string, index: number) => {
    const idx = index % currentLayout.value;
    if (index != 0 && idx == 0) {
      ++rowIndex;
    }
    if (chartMap[rowIndex]) {
      chartMap[rowIndex].push({ paramName, index });
    } else {
      chartMap[rowIndex] = [{ paramName, index }];
    }
  });
  return chartMap;
});

// 计算总高度
const totalHeight = computed(() => {
  const rowNum = Object.keys(newChartList.value).length;
  return selectedPamaterNames.value.length <= currentLayout.value
    ? '100%'
    : chartHeight * rowNum + 'px';
});

const groupId = ref<string[]>([]);
const selectWaferData = ref<LineItem[]>([]);
const selectedPamaterNames = shallowRef<any>([]);
const selectParameterNameOptions = shallowRef<any[]>([]);
const parameterLegend = shallowRef<any[]>([]);
const parameterAndColor = ref<any[]>([]);
const currentType = ref<OverylayType>('Split');
const selectedPamaters = ref<string[]>([]);
const treeData = ref<any[]>([]);

// ==================== 图表相关状态 ====================
const sidebarLoading = ref<boolean>(false);
const splitChartLoading = ref<boolean>(false);
const paramLoadingStates = ref<Map<string, boolean>>(new Map());
const paramRenderStates = ref<Map<string, boolean>>(new Map()); // 跟踪参数是否已渲染
const visibleParams = ref<Set<string>>(new Set()); // 当前可见的参数

// ==================== Group Legend 相关状态 ====================
const groupData = ref<any>({});
const filterInfo = ref<CmFilterObject>({
  type: 'Group',
  filterKeys: []
});

const { customLegendItems, setGroupConfig, initLengend } = useGroupLegend(
  filterInfo,
  customLegendStore,
  selectWaferData
);

// 初始化group legend数据 - 基于实际选中的wafer数据
const initGroupLegend = () => {
  // 获取实际选中的wafer数据中包含的groupId
  let activeGroupIds: string[] = [];
  if (selectWaferData.value && selectWaferData.value.length > 0) {
    activeGroupIds = [
      ...new Set(selectWaferData.value.map((item) => String(item.dataSet)))
    ] as string[];
  } else if (groupId.value && groupId.value.length > 0) {
    // 如果没有选中的wafer数据，则使用所有可用的groupId
    activeGroupIds = groupId.value;
  }

  if (activeGroupIds.length > 0) {
    const groupConfig = activeGroupIds.map((chamberId: string) => {
      const groupConfig = props.commonGroupConfigVO?.groupConfigs?.find(
        (group: any) => String(group.id) === chamberId
      );

      return {
        id: Number(chamberId),
        key: groupConfig?.key || `Chamber ${chamberId}`,
        color:
          groupConfig?.color ||
          groupConfig?.chartLegendColor ||
          `#${Math.floor(Math.random() * 16777215).toString(16)}`
      };
    });

    setGroupConfig(groupConfig);

    // 设置filterInfo包含实际选中的group ID
    const groupIds = activeGroupIds.map((id: string) => Number(id));
    filterInfo.value = {
      type: 'Group',
      filterKeys: groupIds
    };
  }
  initLengend();
};

const chamberList = computed(() => {
  if (
    props.commonGroupConfigVO?.groupConfigs &&
    props.commonGroupConfigVO.groupConfigs.length > 0
  ) {
    return props.commonGroupConfigVO.groupConfigs.map((group: any) => ({
      id: group.id,
      key: group.name,
      color:
        group.color ||
        group.chartLegendColor ||
        `#${Math.floor(Math.random() * 16777215).toString(16)}`
    }));
  } else if (groupData.value && Object.keys(groupData.value).length > 0) {
    return Object.values(groupData.value).map((group: any) => ({
      id: group.id,
      key: group.key,
      color: group.color
    }));
  }
  return [];
});

const selectedGroupColumns = computed(() => {
  if (groupId.value && groupId.value.length > 0 && props.commonGroupConfigVO?.groupConfigs) {
    return props.commonGroupConfigVO.groupConfigs
      .filter((group: any) => groupId.value.includes(String(group.id)))
      .map((group: any) => ({
        id: group.id,
        key: group.name,
        color:
          group.color ||
          group.chartLegendColor ||
          `#${Math.floor(Math.random() * 16777215).toString(16)}`
      }));
  }
  return [];
});

// 处理列变化
const handleChangeColumn = (changeMap: any) => {
  emits('changeColumn', changeMap);
  if (currentType.value === 'Split') {
    nextTick(() => {
      initSplitCharts();
    });
  }
};

// 处理 CustomLegend 过滤图表数据
const filterLotWafer = (event: LineItem[]) => {
  selectWaferData.value = event;
  initGroupLegend();
  nextTick(() => {
    initSplitCharts();
  });
};
const initData = shallowRef<any[]>([]);
const cmOverlayTraceChartRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(cmOverlayTraceChartRef as any);
const isShowBaseline = ref<boolean>(false);

// ==================== 其他状态 ====================
const stepInfo: any = ref([]);
const filterStepValues = ref<string[]>([]);
const xAxisValue = ref<string>('step_count_slot');
const normalization = ref<boolean>(false);

// ==================== Split模式相关代码 ====================
const splitChartData = ref<Map<string, any>>(new Map());
const splitChartInstances = ref<Map<string, any>>(new Map());
const splitChartRefs = ref<Map<string, any>>(new Map());

// Split模式： group-configs 数据
const splitGroupConfigs = computed(() => {
  if (currentType.value !== 'Split' || !selectedPamaterNames.value.length) {
    return [];
  }
  return selectedPamaterNames.value.map((paramName: string, index: number) => {
    const paramColor = getSplitParameterColor(paramName);
    const color = paramColor?.[0]?.chartLegendColor || '#007FAC';

    return {
      id: index + 1,
      key: paramName,
      color
    };
  });
});

// Split模式：布局切换
const currentLayout = ref<number>(1); // 默认一行显示1个

const handleLayoutClick = async (layoutIndex: number) => {
  if (!selectedPamaterNames.value.length) return;

  splitChartLoading.value = true;
  currentLayout.value = layoutIndex;

  // 清理之前的观察状态
  paramRenderStates.value.clear();
  visibleParams.value.clear();

  await nextTick();
  await initSplitCharts();

  // 布局切换后重新观察图表元素
  await nextTick();
  if (intersectionObserver) {
    observeChartElements();
  }
};

const getSplitChartItemStyle = (index: number) => {
  const itemsPerRow = currentLayout.value;
  const rowIndex = Math.floor(index / itemsPerRow);
  const colIndex = index % itemsPerRow;

  return {
    gridRow: `${rowIndex + 1}`,
    gridColumn: `${colIndex + 1}`,
    width: '100%',
    height: '100%'
  };
};

// Split模式：获取图表尺寸
const getChartSize = (paramName?: string) => {
  let chartContainer;
  if (paramName) {
    chartContainer = document.querySelector(`[data-chart="${paramName}"]`);
  } else {
    chartContainer = document.querySelector('.split-chart-item');
  }

  if (!chartContainer) return { width: 800, height: 400 };

  const width = chartContainer.clientWidth - 28;
  let height = chartContainer.clientHeight - 28 - 30;

  height = Math.min(height, 900);
  return { width, height };
};

const onSplitpanesResized = () => {
  nextTick(() => {
    splitChartInstances.value.forEach((chart, paramName) => {
      if (chart) {
        const { width, height } = getChartSize(paramName);
        const chartData = splitChartData.value.get(paramName);
        if (chartData) {
          chartData.options.width = width;
          chartData.options.height = height;
        }
        if (chart.options) {
          chart.options.width = width;
          chart.options.height = height;
        }
        if (typeof chart.setData === 'function') {
          chart.setData(chart.data, false);
        }
      }
    });
  });
};

// Split模式：为每个参数初始化图表
const initSplitCharts = debounce(async () => {
  if (currentType.value !== 'Split' || !selectedPamaterNames.value.length) return;
  splitChartLoading.value = true;
  clearSplitCharts();

  try {
    for (const [index, paramName] of selectedPamaterNames.value.entries()) {
      const paramData = getSplitChartData(paramName);

      if (!paramData || paramData.length === 0) {
        splitChartData.value.delete(paramName);
        continue;
      }

      const cloneDeepParamChartData = cloneDeep(paramData);

      const groupConfigColor = selectedGroupColumns.value.map(({ id, color }) => ({
        id,
        chartLegendColor: color
      }));

      let normalizationValue = [];

      if (normalization.value) {
        normalizationValue = await setNormalization(
          cloneDeepParamChartData,
          filterInfo.value,
          customLegendItems.value
        );
      }

      const { xChartData, seriesColor, xAxisMinMax, yAxisMinMax, tooltipData, newChartData, xMap } =
        await formatToXChart({
          initChartData: cloneDeepParamChartData,
          xyKey: TRACE_CHART_X_AXIS_TYPE.find(({ value }) => value === xAxisValue.value)
            ?.XYGroup || ['count_slot', 'VALUE'],
          groupConfigColor: groupConfigColor,
          normalizationValue,
          filterInfo: filterInfo.value,
          chartLegendConfig: chartConfigStore.chartLegendConfig,
          customLegendItems: customLegendItems.value,
          isDeleteBlank: isDeleteBlank.value,
          filterLotWaferData: selectWaferData.value,
          groupId: groupId.value,
          commonGroupConfigVO: props.commonGroupConfigVO,
          filterStep:
            filterStepValues.value.length === stepInfo.value?.length ? [] : filterStepValues.value,
          customLegendData: customLegendStore.customLegendData
        });

      // 为每个参数创建独立的图表DOM引用
      const paramChartDOM = shallowRef<XChart>();

      // 获取存储的 Y 轴范围设置
      const storedYRange = yRangeStore.left[paramName];
      const customYAxisMinMax = storedYRange
        ? [storedYRange.yMin ?? yAxisMinMax[0], storedYRange.yMax ?? yAxisMinMax[1]]
        : yAxisMinMax;

      const chartOptions = await getTraceOptions({
        contextIndex: {},
        chartData: { data: xChartData, seriesColor },
        model: xAxisValue.value,
        groupData: groupData.value,
        currentRowInfo: [],
        xChartDOM: paramChartDOM,
        width: getChartSize(paramName).width,
        height: getChartSize(paramName).height,
        originData: newChartData,
        xAxisMinMax,
        yAxisMinMax: customYAxisMinMax,
        tooltipData,
        chartLegendConfig: chartConfigStore.chartLegendConfig,
        commonGroupConfigVO: props.commonGroupConfigVO,
        overlay: false,
        isFullscreen: isFullscreen.value,
        isBaseLine: isShowBaseline.value,
        index: index,
        isDeleteBlank: isDeleteBlank.value,
        xMap,
        filterStep: filterStepValues.value.length === stepInfo?.length ? [] : filterStepValues.value
      });

      chartOptions.hooks = chartOptions.hooks || {};
      chartOptions.hooks.setSeries = [
        (chart: any, seriesIdx: number | null, opts: any) => {
          if (seriesIdx !== null && !opts.show) {
            useChartDomain(chart, 'markData', true);
          }
          // 应用lineWidth设置，与trace-chart保持一致
          const lineWidth = chartConfigStore.chartLegendConfig?.lineWidth || 1;
          if (seriesIdx) {
            chart.series.forEach((s: { width: any }, i: any) => {
              s.width = i == seriesIdx ? lineWidth + 2 : lineWidth;
            });
          } else {
            chart.series.forEach((s: { width: any }) => {
              s.width = lineWidth;
            });
          }
        }
      ];
      chartOptions.hooks.draw = [
        () => {
          const chartInstance = splitChartInstances.value.get(paramName);
          if (chartInstance) {
            useChartDomain(chartInstance, 'markData', true);
            // 更新图表DOM引用，用于tooltip
            paramChartDOM.value = chartInstance;
          }
        }
      ];

      chartOptions.hooks.cursorHandleDrag = [
        (self: XChart, position: { left: number }) => {
          xchartDOM.forEach((dom: XChart, i: number) => {
            if (i === index) return; // 跳过当前图表，避免自己同步自己
            dom.syncRect(false);
            dom.setCursor(
              {
                left: position.left,
                top: 100
              },
              true,
              false
            );
          });
        }
      ];

      // 添加 Y 轴双击事件
      chartOptions.hooks.axisDoubleClick = [
        (_chart: any, axes: any) => {
          onYAxisRangeChange(index, axes.label, paramName);
        }
      ];

      const chartDataToSet = {
        data: xChartData,
        options: chartOptions,
        seriesColor
      };
      splitChartData.value.set(paramName, chartDataToSet);

      // 关闭单个参数的loading状态
      paramLoadingStates.value.set(paramName, false);
    }
  } catch (error) {
    console.error('初始化Split图表失败:', error);
  } finally {
    splitChartLoading.value = false;
    sidebarLoading.value = false;
  }

  // 在图表数据创建完成后，启动懒加载观察
  await nextTick();
  if (!intersectionObserver) {
    createIntersectionObserver();
  }
  if (intersectionObserver) {
    observeChartElements();
    checkAndRenderVisibleCharts();
  }
}, 300);

// Split模式：清除图表数据
const clearSplitCharts = () => {
  splitChartInstances.value.forEach((chart, paramName) => {
    if (chart && typeof chart.destroy === 'function') {
      try {
        chart.destroy();
      } catch (error) {
        console.error(`销毁图表实例失败: ${paramName}`, error);
      }
    }
  });

  // 清空所有数据
  splitChartData.value.clear();
  splitChartInstances.value.clear();
  splitChartRefs.value.clear();

  // 清空全局图表DOM数组
  xchartDOM = [];
};

// Split模式：处理图表创建
let xchartDOM: XChart[] = [];
const handleSplitChartCreate = (paramName: string, chart: any, index: number) => {
  xchartDOM[index] = chart;
  splitChartInstances.value.set(paramName, chart);
};

// Split模式：设置图表引用
const setSplitChartRef = (paramName: string, el: any) => {
  if (el) {
    splitChartRefs.value.set(paramName, el);
  }
};

// Split模式：获取单个参数的数据
const getSplitChartData = (paramName: string) => {
  if (!initData.value || initData.value.length === 0) return [];

  // 先按参数名过滤
  let paramData = initData.value.filter((item: any) => item.paramAlias === paramName);

  // 如果有选中的wafer数据，需要进一步过滤
  if (selectWaferData.value && selectWaferData.value.length > 0) {
    const selectedGroupIds = [
      ...new Set(selectWaferData.value.map((item) => Number(item.dataSet)))
    ];

    paramData = paramData.filter((item: any) => selectedGroupIds.includes(item.groupConfigId));
  }

  return paramData;
};

// Split模式：获取单个参数的颜色配置
const getSplitParameterColor = (paramName: string) => {
  if (!parameterAndColor.value || parameterAndColor.value.length === 0) return [];
  return parameterAndColor.value.filter((item: any) => item.name === paramName);
};

// Split模式：legend状态管理
const splitLegendState = ref<
  Map<string, Array<{ id: string; name: string; color: string; show: boolean }>>
>(new Map());

// Split模式：处理group legend变化
const handleGroupLegendChange = (item: any) => {
  const legendItem = customLegendItems.value.find((legend) => legend.id === item.id);
  if (legendItem) {
    legendItem.show = item.show;
  }

  // 重新初始化所有图表
  nextTick(() => {
    initSplitCharts();
  });
};

// ==================== Overlay模式相关代码 ====================
const overlayLegendState = ref<Array<{ id: number; name: string; color: string; show: boolean }>>(
  []
);

const overlayLegend = computed(() => {
  if (overlayLegendState.value.length === 0 && parameterAndColor.value.length > 0) {
    overlayLegendState.value = parameterAndColor.value.map((item, index) => ({
      id: index,
      name: item.name,
      color: item.color,
      show: true
    }));
  }
  return overlayLegendState.value;
});

// Overlay模式：legend变化处理
const overlayLegendChange = async (item: any) => {
  const legendItem = overlayLegendState.value.find((legend) => legend.id === item.id);
  if (legendItem) {
    legendItem.show = item.show;
  }

  overlayTraceChartRef.value?.initChart();
};

// ==================== 事件处理函数 ====================
const handleHide = () => {
  showModel.value = false;

  // 清理基础状态
  selectedPamaterNames.value = [];
  selectParameterNameOptions.value = [];
  parameterLegend.value = [];
  parameterAndColor.value = [];
  currentType.value = 'Split';
  selectedPamaters.value = [];

  // 清理图表相关状态
  sidebarLoading.value = false;
  splitChartLoading.value = false;
  paramLoadingStates.value.clear();
  paramRenderStates.value.clear();
  visibleParams.value.clear();
  groupData.value = {};
  initData.value = [];
  isShowBaseline.value = false;

  // 清理Split模式相关数据
  clearSplitCharts();
  currentLayout.value = 1;

  // 清理Overlay模式相关数据
  overlayLegendState.value = [];

  // 清理showRight相关数据
  showRight.visible = false;
  showRight.list = [];
  showRight.value = [];
  showRight.active = false;

  // 清理其他状态
  stepInfo.value = [];
  filterStepValues.value = [];
  xAxisValue.value = 'step_count_slot';
  normalization.value = false;
  isOverView.value = false;
  overViewImgList.value = [];
  overViewLoading.value = false;
  if (overViewTimer) {
    clearTimeout(overViewTimer);
    overViewTimer = null;
  }
};

const filterStepChange = () => {
  if (currentType.value === 'Split') {
    nextTick(() => {
      initSplitCharts();
    });
  } else {
    nextTick(() => {
      overlayTraceChartRef.value?.initChart();
    });
  }
};

const getParamterChartData = () => {
  if (selectedPamaterNames.value?.length === 0) {
    showWarning('cm.tips.shouldParameters');
    return;
  }

  sidebarLoading.value = true;
  parameterLegend.value = selectedPamaterNames.value;
  parameterAndColor.value = useParameterColor(selectedPamaterNames.value);

  const params = {
    ...props.websocketParams,
    paramAliasList: selectedPamaterNames.value,
    overlay: true
  };

  emits('handleSendSocket', cloneDeep(params));

  if (currentType.value === 'Split') {
    nextTick(() => {
      initSplitCharts();
    });
  }
};

const handleClearOverlay = () => {
  groupData.value = {};
  initData.value = [];
  selectedPamaterNames.value = [];
  parameterLegend.value = [];
  parameterAndColor.value = [];
  selectedPamaters.value = [];

  // 清空右侧Y轴选择
  showRight.value = [];
  showRight.active = false;
  showRight.list = [];
  clearSplitCharts();
  disconnectIntersectionObserver();
};

const overlayTraceChartRef = useTemplateRef('overlayTraceChartRef');
const autoYRef = ref();

const handleChangeType = (type: OverylayType) => {
  currentType.value = type;
};

const handleTreeSelectChange = (checkedValues: string[]) => {
  // 如果选择被清除（空数组），则清除所有数据
  if (checkedValues.length === 0) {
    handleClearOverlay();
    return;
  }

  const maxLimit = overlayTraceChartMaxLimitParameter.value;
  if (checkedValues.length > maxLimit) {
    checkedValues = checkedValues.slice(0, maxLimit);
    showWarning(t('cm.tips.maxLimitParameters', { count: maxLimit }));
  }

  selectedPamaters.value = checkedValues;
  selectedPamaterNames.value = checkedValues;
  showRight.list = selectedPamaterNames.value.map((item: any) => ({
    label: item,
    value: item
  }));

  // 清空之前的右侧Y轴选择
  showRight.value = [];
  showRight.active = false;

  getParamterChartData();
};

const handleClickSidebar = (code: string) => {
  if (code == 'closeCancel' || code == 'cancel') {
    handleHide();
  }
};

// 处理sidebar宽度变化
const handleSidebarResize = debounce(() => {
  if (currentType.value === 'Split') {
    setTimeout(() => {
      initSplitCharts();
    }, 200);
  } else if (currentType.value === 'Overlay') {
    nextTick(() => {
      overlayTraceChartRef.value?.initChart();
    });
  }
}, 300);

const handleShowBaseline = () => {
  if (!selectedPamaterNames.value.length) return;
  isShowBaseline.value = !isShowBaseline.value;
  nextTick(() => {
    initSplitCharts();
  });
};

// Overview
const isOverView = ref(false);
const overViewImgList = ref([] as string[]);
const overViewLoading = ref(false);
let overViewTimer: NodeJS.Timeout | null = null;
const handleShowOverview = async () => {
  let parameterNum = selectedPamaterNames.value.length;
  if (!parameterNum) {
    showWarning('cm.tips.shouldParameters');
    return;
  }

  isOverView.value = true;
  overViewLoading.value = true;
  overViewImgList.value = [];

  try {
    await processOverviewImages(parameterNum);
  } catch (error) {
    console.error('Overview处理失败:', error);
    showError('生成overview失败，请重试');
    isOverView.value = false;
  } finally {
    overViewLoading.value = false;
  }
};

const processOverviewImages = async (parameterNum: number) => {
  await ensureAllChartsRendered();

  await new Promise((resolve) => {
    overViewTimer = setTimeout(resolve, 300);
  });

  const urlList = [] as string[];
  for (let i = 0; i < parameterNum; i++) {
    const elementId = 'overlay-mode-trace-chart-' + i;

    try {
      const _imgUrl = await htmlToImgUrl(elementId);
      urlList.push(_imgUrl);
    } catch (error) {
      console.error(`截图失败: ${elementId}`, error);
    }
  }

  overViewImgList.value = urlList;
};

const ensureAllChartsRendered = async () => {
  if (currentType.value !== 'Split') return;

  // for (let i = 0; i < selectedPamaterNames.value.length; i++) {
  //   const paramName = selectedPamaterNames.value[i];
  //   const hasData = splitChartData.value.has(paramName);

  //   if (!hasData) {
  //    await initSingleSplitChart(paramName, i);
  //   }
  // }

  for (let i = 0; i < selectedPamaterNames.value.length; i++) {
    const paramName = selectedPamaterNames.value[i];
    const hasData = splitChartData.value.has(paramName);

    if (hasData) {
      paramRenderStates.value.set(paramName, true);
      visibleParams.value.add(paramName);
    }
  }

  await nextTick();

  const maxWaitTime = 5000;
  const checkInterval = 100;
  let waitTime = 0;

  while (waitTime < maxWaitTime) {
    const allRendered = selectedPamaterNames.value.every((paramName: string) => {
      const hasData = splitChartData.value.has(paramName);
      const isRendered = paramRenderStates.value.get(paramName);
      const hasInstance = splitChartInstances.value.has(paramName);

      return hasData && isRendered && hasInstance;
    });

    if (allRendered) {
      break;
    }

    await new Promise((resolve) => setTimeout(resolve, checkInterval));
    waitTime += checkInterval;
  }

  await new Promise((resolve) => setTimeout(resolve, 500));
};

const htmlToImgUrl = async (id: string) => {
  const container = document.getElementById(id);

  if (!container) {
    throw new Error(`Element with id "${id}" not found`);
  }

  const result = await snapdom(container, {
    scale: 1,
    backgroundColor: '#fff'
  });
  const img = await result.toPng();
  return img.src;
};
watch(
  () => isOverView.value,
  (val) => {
    if (!val) {
      overViewTimer = null;
    }
  }
);

// Delete Blank
let isDeleteBlank = ref(false);
const handleDeleteBlank = () => {
  if (!selectedPamaterNames.value.length || xAxisValue.value !== 'TIME') return;
  isDeleteBlank.value = !isDeleteBlank.value;
  if (currentType.value === 'Split') {
    nextTick(() => {
      initSplitCharts();
    });
  }
};

//isShowRightY
const showRight = reactive<{
  visible: boolean;
  list: { label: string; value: string }[];
  value: string[];
  active: boolean;
}>({
  visible: false,
  list: [],
  value: [],
  active: false
});

const confirmRightY = () => {
  if (!selectedPamaterNames.value.length) return;
  showRight.active = !!showRight.value.length;
  showRight.visible = false;
  overlayTraceChartRef.value?.initChart();
};

const getMSCTooltip = (omittedValues: any[]) => {
  let tooltip = '';
  omittedValues.map((item, i) => {
    tooltip += item.value;
    tooltip += i < omittedValues.length - 1 ? ', ' : '';
  });
  return tooltip;
};

// ==================== 工具函数 ====================

const customFilter = (input: string, treeNode: any) => {
  return treeNode.parameterName.toLowerCase().includes(input.toLowerCase());
};

const commonFilterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// ==================== 数据初始化 ====================
const modelInit = async () => {
  const params = {
    ...props.websocketParams
  };
  if (params?.requestId && params?.requestDt) {
    const readonlyData = readonly(props.tableData);
    selectParameterNameOptions.value = readonlyData.map(({ groupId, parameterName }: any) => ({
      label: parameterName,
      value: parameterName,
      isGroup: groupId === null
    }));
    treeData.value = props.treeData;
  }
};

const sendOverlayData = async (socketChartData: any) => {
  if (socketChartData?.chartData?.length >= socketChartData?.groupData?.size) {
    groupData.value = { ...groupData.value, ...socketChartData?.groupData };
    const newData = await groupWebSocketData(socketChartData.chartData);
    initData.value = [...initData.value, ...newData];

    if (currentType.value === 'Overlay') {
      await nextTick();
      overlayTraceChartRef.value?.initChart();
      sidebarLoading.value = false;
    } else if (currentType.value === 'Split') {
      await nextTick();
      await initSplitCharts();
    }
  } else {
    groupData.value = {};
    initData.value = [];
  }
};

const cachedGroupConfigColor = computed(() =>
  selectedGroupColumns.value.map(({ id, color }) => ({
    id,
    chartLegendColor: color
  }))
);

const cachedXyKey = computed(
  () =>
    TRACE_CHART_X_AXIS_TYPE.find(({ value }) => value === xAxisValue.value)?.XYGroup || [
      'count_slot',
      'VALUE'
    ]
);

let intersectionObserver: IntersectionObserver | null = null;

const createIntersectionObserver = () => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
  }

  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const element = entry.target as HTMLElement;
        const paramName = element.dataset.paramName;

        if (paramName && entry.isIntersecting) {
          const hasData = splitChartData.value.has(paramName);
          const isRendered = paramRenderStates.value.get(paramName);

          if (hasData && !isRendered) {
            paramRenderStates.value.set(paramName, true);
            visibleParams.value.add(paramName);
            intersectionObserver?.unobserve(element);
          }
        }
      });
    },
    {
      root: null,
      rootMargin: '200px',
      threshold: 0.1
    }
  );
};

// 检查已经在视口内的图表并立即渲染
const checkAndRenderVisibleCharts = () => {
  selectedPamaterNames.value.forEach((paramName: string) => {
    const index = selectedPamaterNames.value.indexOf(paramName);
    const element = document.getElementById(`trace-chart-${paramName}-${index}`);

    if (element) {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
      const hasData = splitChartData.value.has(paramName);
      const isRendered = paramRenderStates.value.get(paramName);

      if (hasData && !isRendered && isVisible) {
        paramRenderStates.value.set(paramName, true);
        visibleParams.value.add(paramName);
      }
    }
  });
};

// 观察所有图表元素
const observeChartElements = () => {
  if (!intersectionObserver || currentType.value !== 'Split') return;

  selectedPamaterNames.value.forEach((paramName: string) => {
    const index = selectedPamaterNames.value.indexOf(paramName);
    const element = document.getElementById(`trace-chart-${paramName}-${index}`);

    if (element) {
      element.dataset.paramName = paramName;
      intersectionObserver?.observe(element);
    }
  });
};

// 停止观察所有元素
const disconnectIntersectionObserver = () => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
  }
};

// ==================== Y 轴范围设置 ====================
// Y 轴范围设置存储 - 用于 Split 模式
const yRangeStore = reactive({
  left: {} as Record<string, { yMin?: number; yMax?: number }>,
  right: {} as Record<string, { yMin?: number; yMax?: number }>
});

// Y 轴范围设置存储 - 用于 Overlay 模式
const overlayYRangeStore = reactive({
  left: { yMin: undefined as number | undefined, yMax: undefined as number | undefined },
  right: { yMin: undefined as number | undefined, yMax: undefined as number | undefined }
});

// Y 轴范围设置弹窗数据
const yRangeData = reactive({
  isShow: false,
  yAxis: [] as any[],
  title: '',
  isSingle: false,
  splitIndex: -1,
  activeY: ''
});

// Overlay模式Y轴双击弹窗数据
const overlayYRangeData = reactive({
  isShow: false,
  yAxis: [] as any[],
  title: '',
  activeY: ''
});

// 清除所有参数的自定义Y轴范围
const clearCustomYRange = () => {
  selectedPamaterNames.value.forEach((paramName: string) => {
    delete yRangeStore.left[paramName];
    delete yRangeStore.right[paramName];
  });
};

// 设置参数的自定义Y轴范围
const setCustomYRange = (
  minLeftY: number | undefined,
  maxLeftY: number | undefined,
  minRightY: number | undefined,
  maxRightY: number | undefined
) => {
  selectedPamaterNames.value.forEach((paramName: string) => {
    // 设置左侧Y轴范围
    if (minLeftY !== undefined || maxLeftY !== undefined) {
      yRangeStore.left[paramName] = {
        yMin: minLeftY,
        yMax: maxLeftY
      };
    }

    // 设置右侧Y轴范围
    if (minRightY !== undefined || maxRightY !== undefined) {
      yRangeStore.right[paramName] = {
        yMin: minRightY,
        yMax: maxRightY
      };
    }
  });
};

// 重新渲染图表
const refreshCharts = () => {
  nextTick(() => {
    if (currentType.value === 'Split') {
      initSplitCharts();
    } else if (currentType.value === 'Overlay') {
      overlayTraceChartRef.value?.initChart();
    }
  });
};

// ==================== Y轴范围设置相关函数 ====================

/**
 * 处理Split模式的Y轴范围设置
 * @param yRange Y轴范围参数
 * @param isAuto 是否为自动模式
 */
const handleSplitModeYRange = (
  yRange: {
    minLeftY: undefined | number;
    maxLeftY: undefined | number;
    minRightY: undefined | number;
    maxRightY: undefined | number;
  },
  isAuto: boolean
) => {
  if (isAuto) {
    clearCustomYRange();
  } else {
    setCustomYRange(yRange.minLeftY, yRange.maxLeftY, yRange.minRightY, yRange.maxRightY);
  }
  refreshCharts();
};

/**
 * 处理Overlay模式的Y轴范围设置
 * @param yRange Y轴范围参数
 * @param isLeftAuto 左侧Y轴是否为自动模式
 * @param isRightAuto 右侧Y轴是否为自动模式
 */
const handleOverlayModeYRange = (
  yRange: {
    minLeftY: undefined | number;
    maxLeftY: undefined | number;
    minRightY: undefined | number;
    maxRightY: undefined | number;
  },
  isLeftAuto: boolean,
  isRightAuto: boolean
) => {
  // 处理左侧Y轴范围
  if (isLeftAuto) {
    // 左侧自动模式：清除左侧自定义范围
    overlayYRangeStore.left = { yMin: undefined, yMax: undefined };
  } else {
    // 左侧手动模式：设置左侧自定义范围
    if (yRange.minLeftY !== undefined || yRange.maxLeftY !== undefined) {
      overlayYRangeStore.left = { yMin: yRange.minLeftY, yMax: yRange.maxLeftY };
    }
  }

  // 处理右侧Y轴范围
  if (isRightAuto) {
    // 右侧自动模式：清除右侧自定义范围
    overlayYRangeStore.right = { yMin: undefined, yMax: undefined };
  } else {
    // 右侧手动模式：设置右侧自定义范围
    if (yRange.minRightY !== undefined || yRange.maxRightY !== undefined) {
      overlayYRangeStore.right = { yMin: yRange.minRightY, yMax: yRange.maxRightY };
    }
  }

  // 重新渲染overlay图表
  nextTick(() => {
    overlayTraceChartRef.value?.initChart();
  });
};

/**
 * 应用Y轴范围设置
 * @param minLeftY 左侧Y轴最小值
 * @param maxLeftY 左侧Y轴最大值
 * @param minRightY 右侧Y轴最小值
 * @param maxRightY 右侧Y轴最大值
 */
const applyAutoY = (
  minLeftY: undefined | number,
  maxLeftY: undefined | number,
  minRightY: undefined | number,
  maxRightY: undefined | number
) => {
  const yRange = { minLeftY, maxLeftY, minRightY, maxRightY };

  const isLeftAuto = minLeftY === undefined && maxLeftY === undefined;
  const isRightAuto = minRightY === undefined && maxRightY === undefined;

  if (currentType.value === 'Split') {
    handleSplitModeYRange(yRange, isLeftAuto);
  }

  if (currentType.value === 'Overlay') {
    handleOverlayModeYRange(yRange, isLeftAuto, isRightAuto);
  }
};

// 处理Y轴双击事件
const onYAxisRangeChange = (chartIndex: number, activeY: string, paramName: string) => {
  yRangeData.isShow = true;
  yRangeData.splitIndex = chartIndex;
  yRangeData.isSingle = true;
  yRangeData.title = paramName;
  yRangeData.activeY = activeY;

  // 获取当前图表的Y轴范围设置
  const leftRange = yRangeStore.left[paramName];
  yRangeData.yAxis = [];

  // 只添加左侧Y轴
  yRangeData.yAxis.push({
    label: t('common.title.left'),
    key: 'left',
    yMin: leftRange?.yMin,
    yMax: leftRange?.yMax
  });
};

// 处理Overlay模式Y轴双击事件
const onOverlayYAxisDoubleClick = (axes: any) => {
  overlayYRangeData.isShow = true;
  overlayYRangeData.title = 'Overlay Chart';

  // 根据Y轴类型设置当前激活的Y轴
  const isRightY = axes.side === 3; // 右侧Y轴
  overlayYRangeData.activeY = isRightY ? 'Right Y' : 'Left Y';

  // 在overlay模式下，同时显示左侧Y轴和右侧Y轴两个选项
  overlayYRangeData.yAxis = [
    {
      label: 'Left Y',
      key: 'left',
      yMin: overlayYRangeStore.left.yMin,
      yMax: overlayYRangeStore.left.yMax
    }
  ];

  // 只有在显示右侧Y轴时才添加右侧Y轴选项
  if (showRight.active && showRight.value && showRight.value.length > 0) {
    overlayYRangeData.yAxis.push({
      label: 'Right Y',
      key: 'right',
      yMin: overlayYRangeStore.right.yMin,
      yMax: overlayYRangeStore.right.yMax
    });
  }
};

// 确认Y轴范围设置
const confirmYAxisRange = (yAxisList: any) => {
  // Split 模式：设置特定图表的 Y 轴范围
  if (yRangeData.isSingle) {
    yAxisList.forEach(({ key, yMin, yMax }: { key: string; yMin: number; yMax: number }) => {
      const isLeft = key === 'left';
      yRangeStore[isLeft ? 'left' : 'right'][yRangeData.title] = {
        yMin,
        yMax
      };
    });
  }

  // 重新渲染图表
  refreshCharts();
};

// 确认Overlay模式Y轴范围设置
const confirmOverlayYAxisRange = (yAxisList: any) => {
  overlayYRangeData.isShow = false;

  yAxisList.forEach(({ key, yMin, yMax }: { key: string; yMin: number; yMax: number }) => {
    if (key === 'left') {
      overlayYRangeStore.left = { yMin, yMax };
    } else if (key === 'right') {
      overlayYRangeStore.right = { yMin, yMax };
    }
  });

  // 重新渲染overlay图表
  nextTick(() => {
    overlayTraceChartRef.value?.initChart();
  });
};

// ==================== 监听器 ====================
watch(
  () => props.treeData,
  (newTreeData) => {
    treeData.value = newTreeData || [];
  },
  { immediate: true }
);

watch(
  () => groupData.value?.stepInfo,
  () => {
    stepInfo.value = groupData.value?.stepInfo?.map(({ step, color }: any) => ({
      step,
      color
    }));
  },
  { immediate: true }
);

watch([() => props.waferData], () => modelInit(), {
  immediate: true
});

watch(
  () => selectWaferData.value,
  async (newSelectWaferData, oldSelectWaferData) => {
    // 更新group legend，只显示实际选中数据的group
    initGroupLegend();

    if (currentType.value === 'Split') {
      await initSplitCharts();
    }
  },
  { deep: true }
);

watch(
  [
    () => currentType.value,
    () => selectedPamaterNames.value,
    xAxisValue,
    normalization,
    () => props.websocketParams?.requestId
  ],
  async (newValues, oldValues) => {
    const [currentType, selectedParams, newXAxisValue, newNormalization] = newValues;
    const [oldCurrentType, oldSelectedParams, oldXAxisValue, oldNormalization] = oldValues || [];
    filterStepValues.value = [];
    if (
      currentType === 'Split' &&
      (oldCurrentType !== currentType ||
        JSON.stringify(selectedParams) !== JSON.stringify(oldSelectedParams) ||
        oldXAxisValue !== newXAxisValue ||
        newNormalization !== oldNormalization)
    ) {
      if (selectedParams && selectedParams.length > 0) {
        parameterAndColor.value = useParameterColor(selectedParams);
      }
      await initSplitCharts();
    } else if (currentType === 'Overlay') {
      if (
        oldXAxisValue !== newXAxisValue ||
        oldCurrentType !== currentType ||
        JSON.stringify(selectedParams) !== JSON.stringify(oldSelectedParams)
      ) {
        getParamterChartData();
      }
    }
  }
);

watch(
  () => initData.value,
  async (newData, oldData) => {
    if (currentType.value === 'Split' && newData.length > 0 && newData.length !== oldData?.length) {
      if (selectedPamaterNames.value.length > 0) {
        await initSplitCharts();
      }
    }
  }
);

// 监听group相关数据变化，初始化group legend
watch(
  [() => props.commonGroupConfigVO?.groupConfigs, () => groupId.value],
  () => {
    initGroupLegend();
    // 如果是Split模式，需要重新渲染图表
    if (currentType.value === 'Split' && selectedPamaterNames.value.length > 0) {
      nextTick(() => {
        initSplitCharts();
      });
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => isFullscreen.value,
  () => {
    if (currentType.value === 'Split') {
      setTimeout(() => {
        initSplitCharts();
      }, 300);
    } else if (currentType.value === 'Overlay') {
      nextTick(() => {
        overlayTraceChartRef.value?.initChart();
      });
    }
  }
);

watch(
  () => parameterAndColor.value,
  (newParameterAndColor) => {
    if (newParameterAndColor.length > 0) {
      overlayLegendState.value = newParameterAndColor.map((item, index) => ({
        id: index,
        name: item.name,
        color: item.color,
        show: true
      }));
    } else {
      overlayLegendState.value = [];
    }
  },
  { immediate: true }
);

watch([() => chartConfigStore.chartLegendConfig, () => customLegendStore.customLegendData], () => {
  if (currentType.value === 'Split' && selectedPamaterNames.value.length > 0) {
    splitLegendState.value.clear();
    nextTick(() => {
      initSplitCharts();
    });
  }
});

// ==================== 生命周期 ====================

onUnmounted(() => {
  clearSplitCharts();
  disconnectIntersectionObserver();
});

// ==================== 暴露的API ====================
defineExpose({
  show: () => {
    showModel.value = true;
  },
  clear: () => {
    handleClearOverlay();
  },
  sendOverlayData
});
</script>
<template>
  <EqFuseSidebar
    :key="'overlayTraceChart'"
    v-model:open="showModel"
    :title="$t('Overlay Trace Chart')"
    :has-zoom="true"
    :width="sidebarWidth"
    class="dataset-modal sidebar-no-padding"
    @clickbutton="handleClickSidebar"
    @resize="handleSidebarResize"
    drawerContainer=".dataset-modal"
  >
    <template #sidebar_content>
      <div
        ref="cmOverlayTraceChartRef"
        v-isLoading="{
          isShow: sidebarLoading,
          hasButton: false,
          title: 'Loading...'
        }"
        class="cm-overlay-trace-wrapper"
      >
        <div class="overlay-trace-table">
          <OverlayTraceTable
            v-model:selectWaferData="selectWaferData"
            v-model:groupId="groupId"
            :dataSetList="chamberList"
            :is-loop="props.commonGroupConfigVO.isLoop"
            :parameter-and-color="parameterAndColor"
          ></OverlayTraceTable>
        </div>

        <div class="cm-overlay-trace-chart-wrapper">
          <div class="header">
            <div class="select-parameterName flex-row flex-items-center">
              <span class="select-label">Parameters</span>
              <a-tooltip
                :title="!groupId.length ? $t('cm.tips.selectDataSetFirst') : ''"
                placement="top"
              >
                <a-tree-select
                  v-model:value="selectedPamaters"
                  show-search
                  style="width: 500px"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  placeholder="Please select"
                  allow-clear
                  multiple
                  :tree-checkable="true"
                  :tree-data="treeData"
                  :show-checked-strategy="TreeSelect.SHOW_CHILD"
                  tree-node-filter-prop="parameterName"
                  :replace-fields="{ label: 'parameterName', value: 'parameterName' }"
                  :max-tag-count="1"
                  :filter-tree-node="customFilter"
                  :disabled="!groupId.length"
                  @change="handleTreeSelectChange"
                >
                  <template #title="{ parameterName, groupId, treeKey }">
                    <div
                      class="custom-tree-title"
                      :class="getNodeClassName(parameterName, groupId)"
                      style="display: flex; align-items: center"
                    >
                      <span
                        v-if="groupId"
                        :style="{
                          width: '12px',
                          height: '12px',
                          borderRadius: '2px',
                          backgroundColor: getChildColor(parameterName),
                          flex: 'none',
                          marginRight: '4px'
                        }"
                      ></span>
                      <span class="parameter-name">{{ parameterName }}</span>

                      <SpecialTag
                        v-if="!groupId"
                        :type="setSpecialTag(findRawGroupByKey(treeKey)).type"
                        :text="setSpecialTag(findRawGroupByKey(treeKey)).text"
                        :custom-style="{
                          padding: '0 8px',
                          width: 'auto',
                          height: '18px',
                          fontSize: '12px',
                          lineHeight: '18px'
                        }"
                        :marginLeft="6"
                      />
                    </div>
                  </template>
                </a-tree-select>
              </a-tooltip>
            </div>
            <div class="flex-row">
              <ees-button-tip
                is-border
                :marginRight="10"
                icon="#icon-btn-clear"
                :text="$t('common.btn.clear')"
                @click="handleClearOverlay"
              />

              <div class="type-btn-container flex">
                <div
                  class="split-btn type-btn-common"
                  :class="{ active: currentType === 'Split' }"
                  @click="handleChangeType('Split')"
                >
                  Split
                </div>
                <div
                  class="overlay-btn type-btn-common"
                  :class="{ active: currentType === 'Overlay' }"
                  @click="handleChangeType('Overlay')"
                >
                  Overlay
                </div>
              </div>
            </div>
          </div>

          <div class="tool">
            <span class="cm-overlay-all-btn-title">Trace Chart</span>
            <vxe-tooltip
              :content="t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
              :use-h-t-m-l="true"
              :theme="baseStore.theme"
            >
              <i
                v-if="cmOverlayTraceChartRef"
                class="iconfont chart_icon_icon zoom-icon fullscreen-icon"
                :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
                @click="toggle"
              ></i>
            </vxe-tooltip>

            <a-select
              v-model:value="filterStepValues"
              show-search
              placeholder="Step Filter"
              mode="multiple"
              :max-tag-count="1"
              :filter-option="commonFilterOption"
              style="min-width: 100px; margin-left: 6px"
              :show-arrow="true"
              allow-clear
              @change="filterStepChange"
            >
              <a-select-option v-for="({ step, color }, i) in stepInfo" :key="i" :value="step">
                <span :style="`color: ${color}`">{{ step }}</span>
              </a-select-option>
            </a-select>

            <div style="flex: 1"></div>

            <a-select
              v-model:value="xAxisValue"
              show-search
              placeholder="Select trace aXis"
              style="width: 290px"
              :options="TRACE_CHART_X_AXIS_TYPE"
              :filter-option="commonFilterOption"
              :show-arrow="true"
            ></a-select>

            <a-space class="normalization">
              <span class="label"> Normalization </span>
              <a-switch v-model:checked="normalization"></a-switch>
            </a-space>

            <ees-button-tip
              v-if="currentType === 'Split'"
              v-for="i in 4"
              :key="i"
              is-border
              :marginRight="10"
              :icon="'#icon-btn-layout-' + i"
              :text="t('eesCharts.commonBtn.layout', { num: i })"
              :is-active="currentLayout === i"
              @click="handleLayoutClick(i)"
            />

            <ees-auto-y
              v-if="currentType === 'Split'"
              ref="autoYRef"
              @apply-auto-y="applyAutoY"
              :isShowRight="false"
              :isShowLog="false"
              :hideAxisSelector="true"
              version="v2"
            />

            <ees-auto-y
              v-if="currentType === 'Overlay'"
              ref="autoYRef"
              @apply-auto-y="applyAutoY"
              :isShowRight="false"
              :isShowLog="false"
              :hideAxisSelector="false"
              version="v2"
            />

            <GroupCustomConfigNew
              v-if="currentType === 'Split' && showModel"
              :group-columns="selectedGroupColumns"
              :websocket-params="websocketParams"
              @changeColumn="handleChangeColumn"
            />

            <CustomLegend
              v-if="currentType === 'Split' && showModel && initData.length > 0"
              :data="initData"
              :groupColumns="selectedGroupColumns"
              :websocketParams="props.websocketParams"
              :isLoop="props.commonGroupConfigVO.isLoop"
              :hiddenCheckbox="true"
              @filterChart="filterLotWafer"
            />

            <ees-button-tip
              v-if="currentType === 'Split'"
              :is-active="isShowBaseline"
              is-border
              :marginRight="10"
              icon="#icon-btn-base-line-show"
              :text="t('eesCharts.commonBtn.baseLine')"
              @click="handleShowBaseline()"
            />

            <a-dropdown
              v-model:visible="showRight.visible"
              :trigger="['click']"
              :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
            >
              <ees-button-tip
                v-if="currentType === 'Overlay'"
                icon="#icon-btn-right-y-axis-setting"
                :text="t('eesCharts.commonBtn.rightY')"
                :is-border="true"
                :margin-right="10"
                :is-active="showRight.active"
              />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-select
                      v-model:value="showRight.value"
                      style="width: 200px; margin-right: 10px"
                      :options="showRight.list"
                      :max-tag-count="1"
                      mode="multiple"
                      allow-clear
                      class="cm-overlay-trace-chart-right-y-select"
                      :not-found-content="t('common.tip.noData')"
                    >
                      <template #maxTagPlaceholder="omittedValues">
                        <a-tooltip :title="getMSCTooltip(omittedValues)" placement="bottom">
                          <span style="font-weight: normal">+{{ omittedValues.length }}</span>
                        </a-tooltip>
                      </template>
                    </a-select>
                    <a-button type="primary" @click="confirmRightY">
                      {{ t('eesBasic.custometime.button.ok') }}
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <EesButtonTip
              v-if="xAxisValue === 'TIME'"
              :margin-right="10"
              :is-border="true"
              icon="#icon-btn-blank-delete"
              :text="$t('eesCharts.commonBtn.deleteBlank')"
              :is-active="isDeleteBlank"
              @on-click="handleDeleteBlank"
            />

            <EesButtonTip
              v-if="currentType === 'Split'"
              :is-border="true"
              :margin-right="10"
              icon="#icon-btn-overview"
              :text="$t('common.btn.overView')"
              @click="handleShowOverview()"
            />
          </div>

          <div
            v-isLoading="{
              isShow: splitChartLoading,
              hasButton: false,
              title: 'Loading...'
            }"
            class="overlay-chart-box flex-col"
            :class="{
              'split-mode': currentType === 'Split',
              'base-line': true,
              fullscreen: isFullscreen && currentType === 'Split'
            }"
          >
            <Splitpanes
              v-if="currentType === 'Split' && selectedPamaterNames.length > 0"
              class="default-theme split-chart-container"
              horizontal
              :style="{
                height: totalHeight,
                position: 'relative'
              }"
              @resized="onSplitpanesResized"
            >
              <Pane v-for="rowIndex in Object.keys(newChartList)" :key="rowIndex">
                <Splitpanes
                  vertical
                  class="default-theme split-chart-row"
                  @resized="onSplitpanesResized"
                >
                  <Pane v-for="chart in newChartList[rowIndex]" :key="chart.paramName">
                    <div
                      :ref="(el) => setSplitChartRef(chart.paramName, el)"
                      :data-chart="chart.paramName"
                      class="split-chart-item"
                      :id="`overlay-mode-trace-chart-${chart.index}`"
                    >
                      <CmChartCopy>
                        <CmGroupLegend
                          :legend="customLegendItems"
                          :title="chart.paramName"
                          :show-checkbox="true"
                          @legendChange="handleGroupLegendChange"
                        >
                          <template #chart>
                            <div
                              :ref="(el) => setSplitChartRef(chart.paramName, el)"
                              :id="`trace-chart-${chart.paramName}-${chart.index}`"
                              class="xchart-draw"
                              style="height: 100%"
                            >
                              <!-- 数据加载中 -->
                              <div
                                v-if="paramLoadingStates.get(chart.paramName)"
                                class="chart-loading"
                                style="
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  height: 100%;
                                "
                              >
                                <a-spin size="large" />
                              </div>
                              <!-- 数据未准备好 -->
                              <div
                                v-else-if="!splitChartData.get(chart.paramName)"
                                class="chart-loading"
                                style="
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  height: 100%;
                                "
                              >
                                <a-spin size="large" />
                              </div>
                              <!-- 数据准备好且已渲染 -->
                              <XChartVue
                                v-else-if="
                                  paramRenderStates.get(chart.paramName) &&
                                  splitChartData.get(chart.paramName)
                                "
                                :options="splitChartData.get(chart.paramName).options"
                                :data="splitChartData.get(chart.paramName).data"
                                @create="
                                  (chartInstance: any) =>
                                    handleSplitChartCreate(
                                      chart.paramName,
                                      chartInstance,
                                      chart.index
                                    )
                                "
                              />
                              <div
                                v-else
                                style="
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  height: 200px;
                                  background: #f5f5f5;
                                "
                              >
                                <a-spin size="large" />
                              </div>
                            </div>
                          </template>
                        </CmGroupLegend>
                      </CmChartCopy>
                    </div>
                  </Pane>
                </Splitpanes>
              </Pane>
            </Splitpanes>

            <!-- Overlay模式：所有参数显示在同一个图表中 -->
            <CmChartCopy v-if="currentType === 'Overlay'">
              <CmGroupLegend
                :show-checkbox="true"
                :legend="overlayLegend"
                :title="`Selected Parameters: ${selectedPamaterNames.length}`"
                @legendChange="overlayLegendChange"
              >
                <template #chart>
                  <div class="overlay-chart-container">
                    <OverlayTraceChart
                      ref="overlayTraceChartRef"
                      :key="`overlay-chart-${currentType}`"
                      v-model:showModel="showModel"
                      :group-id="groupId"
                      :chart-data="initData"
                      :group-data="groupData"
                      :select-wafer-data="selectWaferData"
                      :parameter-legend="overlayLegend"
                      :parameter-and-color="parameterAndColor"
                      :common-group-config-v-o="commonGroupConfigVO"
                      :xAxisValue="xAxisValue"
                      :normalization="normalization"
                      :type="currentType"
                      :isDeleteBlank="isDeleteBlank"
                      :isShowRightY="showRight.active"
                      :showRightYValue="showRight.value"
                      :filter-step="filterStepValues"
                      :step-info="stepInfo"
                      :custom-y-axis-range="overlayYRangeStore"
                      @y-axis-double-click="onOverlayYAxisDoubleClick"
                    />
                  </div>
                </template>
              </CmGroupLegend>
            </CmChartCopy>
          </div>
        </div>
      </div>
    </template>
  </EqFuseSidebar>

  <over-view
    v-model:is-over-view="isOverView"
    :img-list="overViewImgList"
    :loading="overViewLoading"
    load-name="Overlay_Trace_Chart"
  ></over-view>

  <!-- Y 轴范围设置弹窗 -->
  <ees-auto-multi-y
    v-model:is-show="yRangeData.isShow"
    :active-key="yRangeData.activeY"
    :y-axis="yRangeData.yAxis"
    :title="yRangeData.title"
    @confirm-y-axis="confirmYAxisRange"
  />

  <!-- Overlay模式Y轴范围设置弹窗 -->
  <ees-auto-multi-y
    v-model:is-show="overlayYRangeData.isShow"
    :active-key="overlayYRangeData.activeY"
    :y-axis="overlayYRangeData.yAxis"
    :title="overlayYRangeData.title"
    @confirm-y-axis="confirmOverlayYAxisRange"
  />
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');

.cm-overlay-trace-wrapper {
  height: 100%;
  background-color: @arrow-bg-color;
  display: flex;
  flex-direction: row;
  overflow: hidden; // 防止整体滚动

  .overlay-trace-table {
    padding: 14px;
    max-width: calc(30% + 60px);
    overflow-y: auto; // 表格区域可以滚动
    flex-shrink: 0; // 防止表格区域被压缩
  }

  .cm-overlay-trace-chart-wrapper {
    display: flex;
    flex-direction: column;
    padding: 14px 0;
    background-color: @arrow-bg-color;
    height: 100%;
    width: 100%;
    color: @text-title-color;
    border-left: solid @border-color 1px;
    overflow: hidden; // 防止整体滚动

    .header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 14px;
      margin: 0 0 8px 0;
      height: 32px;
      flex-shrink: 0; // 防止header被压缩

      .select-parameterName {
        .select-label {
          margin-right: 10px;
          color: @text-sub-color;
        }
      }
    }

    .tool {
      padding: 10px 14px;
      border-top: solid @border-color 1px;
      border-bottom: solid @border-color 1px;
      display: flex;
      align-items: center;
      flex-shrink: 0; // 防止tool区域被压缩
      .cm-overlay-all-btn-title {
        font-size: 16px;
        font-weight: bold;
        color: @text-title-color;
        margin: 0 4px;
      }
      .fullscreen-icon {
        color: @text-weak-text;
        width: 20px;
        height: 20px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &:hover {
          cursor: pointer;
          color: @text-weak-text;
          background-color: @bg-hover-2-color;
        }
      }
      .normalization {
        margin: 0 10px;
      }
    }

    .type-btn-container {
      cursor: pointer;
      .type-btn-common {
        height: 32px;
        padding: 6px 16px;
        background-color: transparent;
        color: @text-sub-color;
        transition: all 0.3s ease;

        &:hover:not(.active) {
          color: @primary-color;
        }
      }

      .split-btn {
        border: 1px solid @border-color;
        border-radius: 4px 0 0 4px;
      }

      .overlay-btn {
        border: 1px solid @border-color;
        border-left: 1px solid @border-color;
        border-radius: 0 4px 4px 0;
      }

      .active {
        color: @primary-color;
        border-color: @primary-color;
        font-weight: bold;
      }
    }

    .overlay-chart-box {
      display: flex;
      flex: 1;
      padding: 14px;
      overflow-y: auto; // 图表区域可以滚动
      overflow-x: hidden; // 防止水平滚动

      &.split-mode {
        display: grid;
        //  grid-template-columns: repeat(v-bind(currentLayout), 1fr);
        gap: 16px;
        align-items: start;
        // 全屏模式下添加滚动支持
        &.fullscreen {
          overflow-y: auto;
          max-height: 100vh;
          padding: 20px;
        }

        .split-chart-item {
          border: 1px solid @border-color;
          border-radius: 8px;
          background-color: @bg-panel-color;
          min-height: 300px;
          width: 100%; // 确保宽度填满网格单元格
          overflow: hidden; // 防止内容溢出

          .split-chart-title {
            font-size: 14px;
            font-weight: 500;
            color: @text-title-color;
            margin-bottom: 8px;
            text-align: center;
            border-bottom: 1px solid @border-color;
            padding-bottom: 8px;
          }
          .split-chart-content {
            flex: 1;
            width: 100%; // 确保内容区域宽度100%
            height: 100%; // 确保高度填满容器

            // 确保图表容器能够正确适应
            .chart-container {
              width: 100% !important;
              height: 100% !important;
            }
          }
        }
      }
      .selected-parameters {
        text-align: center;
        font-family: Nunito Sans;
        font-size: 14px;
        font-weight: bold;
        height: 24px;
        margin-bottom: 8px;
      }

      .chart-header {
        display: flex;
        align-items: center;
        padding: 0 16px;

        .chart-title {
          font-size: 14px;
          font-weight: 500;
          color: @text-title-color;
        }
      }

      // Split模式样式
      .split-charts-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        height: 100%;

        .split-chart-item {
          border: 1px solid @border-color;
          border-radius: @border-radius-basis;
          padding: 12px;
          background-color: @bg-panel-color;

          .split-chart-title {
            font-size: 14px;
            font-weight: 500;
            color: @text-title-color;
            margin-bottom: 8px;
            text-align: center;
            border-bottom: 1px solid @border-color;
            padding-bottom: 8px;
          }
        }
      }

      .overlay-chart-container {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
<style lang="less">
@import url('@/assets/style/variable.less');

/* a-tree-select 下拉菜单样式 */
.ant-select-dropdown .ant-select-tree-treenode {
  width: 100% !important;

  .ant-tree-node-content-wrapper {
    width: 100% !important;
  }
}

/* 通过内部元素的状态来设置父节点样式 */
.ant-select-dropdown
  .ant-select-tree-treenode:has(.custom-tree-title.tree-child-node.un-match-recipe) {
  color: @error-color !important;
  background-color: @fcm-import-deleted-bg !important;

  /* 确保内部所有文本元素都继承颜色 */
  .ant-tree-title,
  .custom-tree-title,
  .parameter-name,
  span {
    color: @error-color !important;
  }
}

.ant-select-dropdown
  .ant-select-tree-treenode:has(.custom-tree-title.tree-child-node.warning-recipe) {
  color: @warning-color !important;
  background-color: @fcm-import-changed-bg !important;

  /* 确保内部所有文本元素都继承颜色 */
  .ant-tree-title,
  .custom-tree-title,
  .parameter-name,
  span {
    color: @warning-color !important;
  }
}

.ant-select-dropdown .ant-select-tree-treenode:has(.custom-tree-title.tree-child-node.col-red) {
  color: @error-color !important;

  /* 确保内部所有文本元素都继承颜色 */
  .ant-tree-title,
  .custom-tree-title,
  .parameter-name,
  span {
    color: @error-color !important;
  }
}

.ant-select-dropdown .ant-select-tree-treenode:has(.custom-tree-title.tree-parent-node) {
  font-weight: 500;
  color: @text-title-color;

  /* 确保内部所有文本元素都继承颜色 */
  .ant-tree-title,
  .custom-tree-title,
  .parameter-name,
  span {
    color: @text-title-color !important;
  }
}

/* 禁用 Ant Design 的默认 hover 效果 */
.ant-select-dropdown .ant-select-tree-treenode:hover {
  background-color: transparent !important;
}

/* 禁用具体的 Ant Design hover 选择器 */
:where(.css-dev-only-do-not-override-gadrfb).ant-tree-select-dropdown
  .ant-select-tree
  .ant-select-tree-node-content-wrapper:hover,
:where(.css-dev-only-do-not-override-gadrfb).ant-tree-select-dropdown
  .ant-select-tree
  .ant-select-tree-checkbox
  + span:hover {
  background-color: transparent !important;
}

/* 自定义树节点样式 - 根据实际使用的类名调整 */
.ant-select-dropdown .custom-tree-title {
  width: 100% !important;
}

/* ==================== Splitpanes 样式 ==================== */
.split-chart-container {
  width: 100%;
  height: 100%;
}

.split-chart-container .splitpanes__pane {
  overflow: hidden;
}

.split-chart-row {
  width: 100%;
  height: 100%;
}

.split-chart-row .splitpanes__pane {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.split-chart-item {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.overlay-chart-box.base-line .xchart-cursor-x {
  border-right: 2px solid #7581bd;
}
</style>

<style lang="less">
.cm-overlay-trace-chart-right-y-select.ant-select {
  .ant-select-selector {
    .ant-select-selection-overflow-item {
      max-width: calc(100% - 60px) !important;
      margin-right: 4px;
    }
  }
}
</style>
