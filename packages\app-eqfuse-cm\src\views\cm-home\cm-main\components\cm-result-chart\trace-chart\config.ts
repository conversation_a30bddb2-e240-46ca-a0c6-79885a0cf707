import { type ChartOptions, type IMarkLineOptions, Series, XChart } from '@xchart/vue';
import { cmChartContextFilter, cmChartLegendFilter, dealYSpace, useGenerateSeriesConfigs } from '@/utils';
import {
  DATA_GROUP,
  CHART_DEFAULT_COLORS,
  CHART_DEFAULT_NEGATIVE_COLORS,
  TRACE_CHART_X_AXIS_TYPE,
  CHART_CONSTANT,
  CM_CHART_DEFUALT_FONT_SIZE,
  CM_CHART_DEFUALT_Y_MAX_TICK
} from '@/constant/charts';
import { type IMarkerItemData } from '@xchart/marker/dist';
import { VXETable } from '@futurefab/vxe-table';
import { getChartAxisRange } from '@/utils';
import { type StepTimeTooltip } from '@/utils';
import moment from 'moment';
import { formatterYSM } from '@/utils';
import { add, subtract, divide } from '@/utils/math';
import {
  useBaseStore,
  type ChartLegendConfig,
  type NewChartConfig
} from '@futurefab/ui-sdk-stores';
import type { CmFilterObject } from '../interface';
import type { CmLegend } from '@/components/cm-group-legend/interface';

// 为series配置添加右侧Y轴支持
const addRightYAxisSupport = (
  seriesConfigs: any[],
  originData: any[],
  showRightYValue?: string[]
): any[] => {
  // 如果没有右侧Y轴参数，直接返回原配置
  if (!showRightYValue || showRightYValue.length === 0) {
    return seriesConfigs;
  }

  // 为每个series配置添加scale属性
  return seriesConfigs.map((config, index) => {
    const paramAlias = originData[index]?.paramAlias;
    const isRightY = showRightYValue.includes(paramAlias);

    return {
      ...config,
      scale: isRightY ? 'y-right' : 'y'
    };
  });
};


const baseStore = useBaseStore();
const baseTimeStamp: number = moment('2000/01/01 00:00:00', 'YYYY/MM/DD HH:mm:ss').valueOf();
// 设置y轴折线对象
const yAxis = {
  label: 'y_name',
  points: { symbol: 'circle', size: 2, show: true, value: (_: any, val: any) => val },
  width: 1
};
const getLineColor = () => {
  return baseStore.theme === 'light' ? '#ECEEF0' : '#556070';
};
const getLabelColor = () => {
  return baseStore.theme === 'light' ? '#4e5969' : '#e2e4e7';
};
// --bg-block-color
const getBackgroundColor = () => {
  return baseStore.theme === 'light' ? '#ffffff' : '#132035';
};
// --fcm-import-deleted-bg
const getUnMatchBackgroundColor = () => {
  return baseStore.theme === 'light' ? '#ffece8' : '#4d000a';
};
// 时间间隔选项组
const timeSlot = ['step_time_slot', 'wafer_time_slot'];
// step选项组
const haveStep = ['step_count_slot', 'step_time_slot'];
// tooltips different from select
const diffCountArr = ['step_count_slot', 'wafer_count_slot'];
const diffTimeArr = ['step_time_slot', 'wafer_time_slot'];
// 设置step标记
const getMarkLine = (
  model: string,
  stepsData: { step: any; startCountSlot: any; startTimeSlot: any }[],
  filterStep?: string[],
  xMap?: any
): any => {
  let temp = stepsData;
  if (filterStep?.length) {
    temp = temp.filter(({ step }) => {
      return !!filterStep.find((fStep) => fStep + '' === step + '');
    });
  }
  if (haveStep.includes(model)) {
    return temp?.map(({ step, startCountSlot, startTimeSlot }) => {
      let x = model === 'step_count_slot' ? startCountSlot : startTimeSlot;
      if (filterStep?.length) {
        let temp = Object.entries(xMap).find(([key, value]: any) => {
          return value == (model === 'step_count_slot' ? startCountSlot : startTimeSlot);
        });
        x = temp?.[0];
      }
      return {
        type: 'max',
        color: getLineColor(),
        size: 1,
        data: { x },
        label: step,
        labelPosition: 'top',
        arrow: false
      };
    });
  }
  return [];
};
const getTraceRecipeStepStatScore = ({
  groupScoreMap,
  paramDatum,
  recipeStep,
  contextKey
}: any) => {
  if (!paramDatum || !groupScoreMap[paramDatum.groupConfigId]) {
    return 'NA';
  } else if (
    !groupScoreMap[paramDatum.groupConfigId][contextKey] ||
    !groupScoreMap[paramDatum.groupConfigId][contextKey][recipeStep]
  ) {
    return '0';
  } else {
    const statScore = groupScoreMap[paramDatum.groupConfigId][contextKey][recipeStep];
    if (statScore.score === 0) return statScore.score;
    return statScore.score + (statScore.stat === '' ? '' : ' (' + statScore.stat + ')');
  }
};
const calculateMedian = (values: number[]): number => {
  if (values.length === 0) return 0;
  values.sort((a, b) => a - b);
  const half = Math.floor(divide(values.length, 2));
  return values.length % 2 ? values[half] : divide(add(values[half - 1], values[half]), 2.0);
};
/**
 * 获取 groupNormMap
 */
const prepareNormValues = (chartData: any): Map<number, number> => {
  const groupDataMap = new Map<number, number[]>();
  const groupIds = new Set<number>();

  for (const data of chartData) {
    const groupId = data.groupConfigId;
    if (!groupDataMap.has(groupId)) {
      groupDataMap.set(groupId, []);
      groupIds.add(groupId);
    }
    // cannot access Y axis value using s.value which is supposed to be available with TraceChartDataPoint interface.
    // So access with indexer value of 1(s[1]) which is actual Y axis value of each data point
    groupDataMap.get(groupId)!.push(...data['VALUE']);
  }

  const groupNormMap = new Map<number, number>();
  for (const groupId of groupIds) {
    const normValue = calculateMedian(groupDataMap.get(groupId)!);
    groupNormMap.set(groupId, normValue);
  }

  return groupNormMap;
};
// 根据x轴刻度拿到对应的tooltip
function formatTooltipX(
  contextIndex: any,
  tooltip: IMarkerItemData[],
  i: number,
  originData: any,
  model: string,
  tooltipData: StepTimeTooltip[],
  groupData: any,
  commonGroupConfigVO: any,
  groupConfigs?: any,
  overlay?: boolean
) {
  const nowOriginData = originData?.[i - 1];
  const nowToolTip: any = tooltipData?.[i - 1];
  const activeAxis = TRACE_CHART_X_AXIS_TYPE.find(({ value }) => value === model)?.XYGroup || [
    'count_slot',
    'VALUE'
  ];
  const { value, dataIndex } = tooltip?.[i] as any;
  const { contextKey, lotId, substrateId, chamber, eqpModuleId, eqpId, loopNo, stationName } =
    nowOriginData;
  const { STEP, TIME } = nowToolTip;

  const recipeStep = STEP?.[dataIndex];
  const stationArr = substrateId?.split(',');
  const groupConfig: any = groupConfigs?.find(
    (item: any) => item.id === nowOriginData.groupConfigId
  );
  const _eqpId = nowOriginData.eqpId || eqpModuleId.split(':')?.[1];
  const tips = [
    `<p style="margin: 0;font-size: 16px;font-weight: 600">Data Details</p>`,
    `<p style="margin: 0;">EQP : ${_eqpId}</p>`,
    `<p style="margin: 0;">Chamber : ${chamber}</p>`,
    `<p style="margin: 0;">Lot : ${lotId}</p>`,
    `<p style="margin: 0;">Wafer : ${substrateId?.split(';')?.[0]}</p>`,
    `<p style="margin: 0;">Recipe Step : ${recipeStep}</p>`,
    `<p style="margin: 0;">DateTime : ${moment(TIME?.[dataIndex]).format(
      CHART_CONSTANT.XAisTimeFormat
    )}</p>`,
    `<p style="margin: 0;">Value : ${value}</p>`
  ];
  if (commonGroupConfigVO?.isLoop) {
    tips.splice(5, 0, `<p style="margin: 0;">Loop : ${loopNo}</p>`);
  } else if (commonGroupConfigVO?.stationCompare) {
    tips.splice(5, 0, `<p style="margin: 0;">Station : ${stationName}</p>`);
  }
  if (!overlay) {
    const groupScoreMap = groupData?.groupScoreMap;
    const groupScoreNormMap = groupData?.groupScoreNormMap;
    const score = groupConfig.reference
      ? 'N/A (Reference)'
      : getTraceRecipeStepStatScore({
          groupScoreMap: groupScoreMap,
          paramDatum: nowOriginData,
          recipeStep,
          contextKey
        });
    const normScore = groupConfig.reference
      ? 'N/A (Reference)'
      : getTraceRecipeStepStatScore({
          groupScoreMap: groupScoreNormMap,
          paramDatum: nowOriginData,
          recipeStep,
          contextKey
        });
    const groupNormMap: any = prepareNormValues(originData);
    const normValue = subtract(value, groupNormMap.get(groupConfig?.id));
    tips.splice(stationArr.length > 1 ? 7 : 6, 0, `<p style="margin: 0;">Score : ${score}</p>`);
    tips.splice(
      stationArr.length > 1 ? 8 : 7,
      0,
      `<p style="margin: 0;">Score(Normalized) : ${normScore}</p>`
    );
    tips.splice(tips.length, 0, `<p style="margin: 0;">Value(Normalized) : ${normValue}</p>`);
  }
  if (diffCountArr.includes(model)) {
    tips.splice(
      stationArr.length > 1 ? 9 : 8,
      0,
      `<p style="margin: 0;">Sequence : ${
        nowToolTip?.[activeAxis?.[0] as keyof StepTimeTooltip]?.[dataIndex]
      }</p>`
    );
  }
  if (diffTimeArr.includes(model)) {
    tips.splice(
      stationArr.length > 1 ? 9 : 8,
      0,
      `<p style="margin: 0;">Relative Time : ${moment(
        nowToolTip?.[activeAxis?.[0] as keyof StepTimeTooltip]?.[dataIndex]
      ).format(CHART_CONSTANT.TIME_SLOT_FORMAT)}</p>`
    );
  }
  return tips.join('');
}

/* xChart baseline */
function roundIncr(x: number, incr: number) {
  return Math.round(x / incr) * incr;
}
const moveTuple = [0, 0];
function cursorMove(self: number, left: number, top: number): any {
  moveTuple[0] = roundIncr(left, 10);
  moveTuple[1] = roundIncr(top, 10);
  return moveTuple;
}

export interface TraceOptions {
  contextIndex: any;
  chartData: any;
  model: string;
  groupData: any;
  currentRowInfo: string[];
  xChartDOM: any;
  width: number;
  height: number;
  originData: any;
  xAxisMinMax: number[];
  yAxisMinMax: number[];
  tooltipData: StepTimeTooltip[];
  chartLegendConfig?: ChartLegendConfig | null | NewChartConfig;
  commonGroupConfigVO?: any;
  overlay?: boolean;
  isFullscreen?: boolean;
  isBaseLine?: boolean;
  index?: number | string;
  isDeleteBlank?: boolean;
  xMap?: any;
  isShowRightY?: boolean;
  showRightYValue?: string[];
  filterStep?: string[];
  highlightStep?: boolean;
  isScreenshotMode?: boolean;
  groupColumns?: any[];
  rightYAxisData?: any[];
  customRightYRange?: [number, number];
  customLeftYRange?: [number | null, number| null] | null;
  seriesLegend?: string[];
  totalCount?: number;
}
// 修改chartOptions参数
export const getTraceOptions = ({
  contextIndex,
  chartData,
  model,
  groupData,
  currentRowInfo,
  xChartDOM,
  width,
  height,
  originData,
  xAxisMinMax,
  yAxisMinMax,
  tooltipData,
  chartLegendConfig,
  commonGroupConfigVO,
  overlay,
  isFullscreen,
  isBaseLine,
  index,
  isDeleteBlank,
  xMap,
  isShowRightY,
  showRightYValue,
  filterStep,
  highlightStep = true,
  isScreenshotMode = false,
  groupColumns,
  rightYAxisData,
  customRightYRange,
  customLeftYRange,
  seriesLegend,
}: TraceOptions): ChartOptions => {
  const stepsData = groupData?.stepInfo;
  const groupConfigs = commonGroupConfigVO?.groupConfigs;
  const { data, seriesColor } = chartData;
  const xRange = getChartAxisRange(xAxisMinMax?.[0], xAxisMinMax[1], 0.01);
  const temp = getChartAxisRange(yAxisMinMax?.[0], yAxisMinMax[1], 0.1);
  const yRange = [customLeftYRange?.[0] ?? temp[0], customLeftYRange?.[1] ?? temp[1]] as [number, number];
  // 字体放大比例
  const fontSize = chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE;
  const maxYTick = (chartLegendConfig as any)?.yAxisMaxTicksLimit || CM_CHART_DEFUALT_Y_MAX_TICK;
  const ratio = fontSize / CM_CHART_DEFUALT_FONT_SIZE;
  // 计算右侧Y轴范围
  let rightYRange: [number, number] | undefined;
  if (isShowRightY && showRightYValue && showRightYValue.length > 0 && overlay) {
    // 如果传入了自定义右侧Y轴范围，直接使用
    if (customRightYRange) {
      rightYRange = customRightYRange;
    } else {
      // 使用完整的原始数据计算右侧Y轴范围
      const dataSource = rightYAxisData || originData;
      const rightYData = dataSource
        .filter((item: any) => showRightYValue.includes(item.paramAlias))
        .flatMap((item: any) => item.VALUE || []);

      if (rightYData.length > 0) {
        const rightYMinMax = [Math.min(...rightYData), Math.max(...rightYData)];
        rightYRange = getChartAxisRange(rightYMinMax[0], rightYMinMax[1], 0.1);
      }
    }
  }

  const options: ChartOptions = {
    type: 'line',
    title: '',
    width,
    height,
    legend: false,
    tooltip: {
      alwaysShowContent: true,
      hideInPointOut: true,
      appendToBody: true,
      formatter: (event: any[]) => {
        const temp: any[] = [];
        event.slice(1, 13).forEach((item) => {
          if (
            item.seriesIndex &&
            item.value !== '' &&
            item.value !== null &&
            item.value !== undefined &&
            !isNaN(Number((item.value + '').replace(/,/g, '')))
          ) {
            const legend = seriesLegend?.[item.seriesIndex - 1];
            const color = item.color;
            legend &&
              temp.push(`<div style="display: flex; align-items: center"> 
                <div style="background: ${color}; height: 12px; width: 12px; border-radius: 50%; margin-right: 5px"> </div>
                <span style="margin-right: 5px">${legend}</span>
                <span>${item.value}</span>
              </div>`);
          }
        });
        return temp.length
          ? `<div style="display: flex; flex-direction: column; max-height: 140px; flex-wrap: wrap; column-gap: 10px">${temp.join('')}</div>`
          : '';
      }
    },
    zoomBar: [],
    zoom: {
      enable: true,
      enableDbClickReset: false,
      enableZoomBackBySelect: true
    },
    background: getBackgroundColor(),
    colors: seriesColor,
    select: {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
      enableDelByRightClick: true
    },
    cursor: isBaseLine
      ? {
          trigger: 'item',
          move: cursorMove
        }
      : undefined,
    toolbox: {
      markData: {
        tooltip: '',
        icon: '',
        disableCursor: !isBaseLine,
        clickOffset: 20,
        closable: true,
        copyable: true,
        disableTooltip: false,
        canMoveOutside: true,
        markByIndex: false,
        single: true,
        formatter: (tooltip: IMarkerItemData[]) =>
          formatTooltipX(
            contextIndex,
            tooltip,
            (xChartDOM.value as any).closestSeries,
            originData,
            model,
            tooltipData,
            groupData,
            commonGroupConfigVO,
            groupConfigs,
            overlay
          )
      }
    },
    axes: [
      {
        scale: 'x',
        stroke: getLabelColor(),
        space: () => {
          if (model === 'TIME') {
            return 60 + 2 * fontSize;
          }
          if (timeSlot.includes(model)) {
            return 70 + 2 * fontSize;
          }
          return 40;
        },
        grid: { show: false },
        border: { show: true, stroke: getLineColor() },
        values: (_: XChart, value: number[]) => {
          return value.map((v) => {
            if (timeSlot.includes(model)) {
              if (filterStep?.length) {
                if (xMap[+v]) {
                  return `${moment(+xMap[+v] + baseTimeStamp).format('HH:mm:ss')}`;
                } else {
                  return '';
                }
              } else {
                return `${moment(+v + baseTimeStamp).format('HH:mm:ss')}`;
              }
            } else if (model === 'TIME') {
              if (!isDeleteBlank) {
                return `${moment(+v).format('YYYY/MM/DD')}\n${moment(+v).format('HH:mm')}`;
              } else {
                if (xMap[+v]) {
                  return `${moment(+xMap[+v]).format('YYYY/MM/DD')}\n${moment(+xMap[+v]).format(
                    'HH:mm'
                  )}`;
                } else {
                  return '';
                }
              }
            }
            if (filterStep?.length) {
              if (xMap[+v]) {
                return xMap[+v];
              } else {
                return '';
              }
            } else {
              return v;
            }
          });
        },
        font: `${fontSize}px Nunito Sans`
      },
      // 左侧Y轴
      {
        side: 3,
        stroke: getLabelColor(),
        grid: { show: false },
        border: { show: true, stroke: getLineColor() },
        drag: false,
        space: dealYSpace(height, maxYTick, fontSize),
        values: (_: any, val: any[]) => val.map((v) => formatterYSM(v, 4)),
        font: `${fontSize}px Nunito Sans`,
        show: true
      },
      // 右侧Y轴
      {
        side: 1,
        scale: 'y-right',
        stroke: getLabelColor(),
        grid: { show: false },
        border: { show: true, stroke: getLineColor() },
        drag: false,
        space: dealYSpace(height, maxYTick, fontSize),
        values: (_: any, val: any[]) => val.map((v) => formatterYSM(v, 4)),
        font: `${fontSize}px Nunito Sans`,
        show: isShowRightY
      }
    ],
    scales: {
      x: {
        time: false,
        range: xRange
      },
      y: {
        range: yRange
      },
      ...(isShowRightY && showRightYValue && showRightYValue.length > 0
        ? {
            'y-right': {
              range: rightYRange
            }
          }
        : {})
    },
    series: [
      {},
      ...addRightYAxisSupport(
        useGenerateSeriesConfigs(data.length - 1, {
          ...yAxis,
          width: chartLegendConfig?.lineWidth || 1,
          points: { ...yAxis.points, size: (chartLegendConfig?.lineWidth || 1) + 1 }
        }),
        originData,
        showRightYValue
      )
    ],
    hooks: {
      beforeDraw: [
        (chart: XChart) => {
          if (!yAxisMinMax) { return }
          const sizeList = [
            { limit: 10, size: 8},
            { limit: 100, size: 6},
            { limit: 1000, size: 4},
            { limit: 10000, size: 2},
            { limit: Infinity, size: 1},
          ];
          // 获取所有scale的比例 {x: {min: 0.1, max: 0.5}, y: {min: 0, max: 1}}  
          const scalesRatio = (chart as any).calcScaleRatios?.();
          const curXCount = (scalesRatio.x.max - scalesRatio.x.min) * chartData.data[0].length;
          const curYRatio = scalesRatio.y.max - scalesRatio.y.min;
          const avgYCount = Math.max((chart.series.length - 1) * curYRatio, 1);
          // 估计的可视区域值
          const curCount = avgYCount * curXCount;
          const resultSize = sizeList.find(item => item.limit > curCount)?.size;
          chart.series.forEach((series: any, idx: number) => {
              if (idx === 0) {
                  return;
              }
              series!.points!.size = Math.min((chartLegendConfig?.lineWidth || 1) + (resultSize || 1), 15);
          });
      },
      ],
      draw: [],
      setSeries: [],
      cursorHandleDrag: []
    },
    padding: [
      20 * ratio,
      32 * ratio,
      model === 'TIME' ? (9 / 4) * fontSize - 30 : (3 / 4) * fontSize - 29,
      8 * ratio
    ]
  };
  options.markLine = getMarkLine(model, stepsData, filterStep, xMap);

  if (stepsData && stepsData?.length > 0 && options.markLine!.length > 0) {
    // const specialStep = ['21', '22', '23', '24', '7'];
    const coordValues: any = [];

    const filterStepData = stepsData.filter(
      ({ step }: any) => !filterStep?.length || filterStep?.includes(step)
    );
    filterStepData.forEach(({ step, startCountSlot, startTimeSlot }: any, i: number) => {
      if (currentRowInfo.includes(step) && (!filterStep?.length || filterStep?.includes(step))) {
        let start = model === 'step_count_slot' ? startCountSlot : startTimeSlot;
        let end =
          model === 'step_count_slot'
            ? filterStepData[i + 1]?.startCountSlot || null
            : filterStepData[i + 1]?.startTimeSlot || null;
        if (filterStep?.length) {
          Object.entries(xMap).forEach(([key, value]: any) => {
            if (value === start) {
              start = key;
            }
            if (value === end) {
              end = key;
            }
          });
          if (end === null) {
            end = chartData.data[0]?.length - 1;
          }
        } else {
          if (end === null) {
            end = xRange[1];
          }
        }

        coordValues.push(start);
        coordValues.push(end);
      }
    });

    coordValues.forEach((value: any, index: number) => {
      if ((index + 1) % 2 === 0) {
        options.hooks!.beforeDraw!.push((u: any) => {
          let shouldDraw = false;
          if (!isScreenshotMode) {
            // 正常页面，始终绘制
            shouldDraw = true;
          } else {
            // 截图页面，根据 highlightStep 决定
            shouldDraw = highlightStep;
          }

          if (shouldDraw) {
            u.drawAreaPoints(
              [
                { x: value, y: yRange?.[0] },
                { x: coordValues[index - 1], y: yRange?.[0] },
                { x: coordValues[index - 1], y: yRange?.[1] },
                { x: value, y: yRange[1] }
              ],
              getUnMatchBackgroundColor()
            );
          }
        });
      }
    });
  }

  return options;
};
/**
 * format trance chart data
 * @returns trance chart table data
 */
export const formatToTable = (originData: any, groupColumns: any) => {
  const tableData: any = [];
  let groupInfo: any = [];
  originData.forEach((origin: any) => {
    groupInfo = groupColumns.find(({ id }: any) => Number(id) === origin.groupConfigId);
    origin?.STEP.forEach((step: string, i: number) => {
      tableData.push({
        key: groupInfo?.key,
        wafer: origin.substrateId?.split(';')[0],
        groupConfigId: origin.groupConfigId,
        eqp: groupInfo?.EQP,
        chamber: groupInfo?.Chamber,
        lotId: origin.lotId,
        substrateId: origin.substrateId,
        recipeStep: step,
        time: moment.unix(origin?.TIME[i] / 1000).format(CHART_CONSTANT.TraceTimeFormat),
        value: origin?.VALUE[i]
      });
    });
  });
  return tableData;
};
/**
 * setting trance chart table column
 */
export const getViewDataTableOptions = () => {
  return VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      tableName: 'common.title.viewData',
      import: false,
      export: true,
      refresh: true // 为了显示 custom;  import, export refresh至少有一个true, 用样式隐藏
    },
    columns: [
      {
        field: 'key',
        title: 'cm.field.dataSet',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'eqp',
        title: 'cm.field.eqp',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'chamber',
        title: 'cm.field.chamber',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'lotId',
        title: 'cm.field.lot',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'wafer',
        title: 'cm.field.wafer',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'recipeStep',
        title: 'cm.field.recipeStep',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'time',
        title: 'cm.field.time',
        minWidth: 120,
        sortable: true,
        filters: [{ data: '' }],
        filterRender: { name: '$input' }
      },
      {
        field: 'value',
        title: 'cm.field.value',
        minWidth: 120,
        sortable: true,
        filters: [{ data: '' }],
        filterRender: { name: '$input' }
      }
    ]
  });
};
/**
 * setting trance chart filter column
 */
export const getFilterFieldList = () => {
  return ['key', 'eqp', 'chamber', 'lotId', 'wafer', 'recipeStep'];
};

/**
 * change normalization
 */
export const setNormalization = (
  chartData: any,
  filterInfo: CmFilterObject,
  customLegendItems: CmLegend[]
) => {
  const valueList = [];
  let chartDataList = [];
  if (filterInfo.filterKeys?.length > 0) {
    // chartData
    chartDataList = chartData?.filter(
      ({ groupConfigId, eqpId, chamber, recipeId }: any) =>
        cmChartContextFilter(filterInfo as CmFilterObject, {
          group: groupConfigId,
          eqpId,
          chamber,
          recipeId
        }) && cmChartLegendFilter(customLegendItems, groupConfigId)
    );
  } else {
    chartDataList = chartData;
  }
  const groupNormMap = prepareNormValues(chartDataList);
  for (const data of chartDataList) {
    const normValue = groupNormMap.get(data.groupConfigId) || 0;
    const tempValueList = data['VALUE'].map((val: number) => subtract(val, normValue));
    valueList.push(tempValueList);
  }

  return valueList;
};
