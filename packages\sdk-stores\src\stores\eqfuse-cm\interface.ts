export interface GroupLegendConfig {
  groupName: string;
  alias: string;
  defaultColor: string;
  customColor: string;
  id: number;
}

export interface ChartLegendConfig {
  groupLegendConfigs: GroupLegendConfig[];
  legendType: 'byGroup' | 'Customize';
  lineWidth: number;
  coordinateFontSize: number;
}
export interface NewChartConfig {
  lineWidth: number;
  coordinateFontSize: number;
  yAxisMaxTicksLimit: number;
}
export interface CmConfigState {
  smartMachineLocalMode: string;
  trimmingOptionMaxCount: number;
  trimmingOptionMaxPercent: number;
  trimmingOptionMaxTime: number;
  scheduleJobResultPageSize: number;
  cmGlobalRequestTimeout: number | string;
  cmAnalysisRequestTimeout: number | string;
}
