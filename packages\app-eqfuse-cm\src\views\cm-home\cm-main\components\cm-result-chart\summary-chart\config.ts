import { type ChartOptions, XChart } from '@xchart/vue';
import { VXETable } from '@futurefab/vxe-table';
import {
  CHART_CONSTANT,
  SUMMARY_CHART_STATE_TYPES,
  DATA_GROUP,
  CM_CHART_DEFUALT_FONT_SIZE,
  CM_CHART_DEFUALT_Y_MAX_TICK
} from '@/constant/charts';
import moment from 'moment';
import { dealYSpace, getChartAxisRange } from '@/utils';
import { formatterYSM } from '@/utils';

import { useBaseStore, type NewChartConfig } from '@futurefab/ui-sdk-stores';

const baseStore = useBaseStore();

const getLineColor = () => {
  return baseStore.theme === 'light' ? '#ECEEF0' : '#556070';
};
const getLabelColor = () => {
  return baseStore.theme === 'light' ? '#4e5969' : '#e2e4e7';
};
// --bg-block-color
const getBackgroundColor = () => {
  return baseStore.theme === 'light' ? '#ffffff' : '#132035';
};
// 获取y轴的属性详细信息
const getYAxisInfo = (chartData: any[], xChartDOM: any): any => {
  return chartData?.slice(1)?.map((data, index) => ({
    label: 'Value' + index,
    points: {
      connect: false,
      symbol: 'hexagon',
      size: 8
    },
    spanGaps: true,
    lotWafer: data?.[2]
  }));
};
// 根据x轴刻度拿到对应的tooltip
const formatTooltipX = (
  tooltip: any,
  chartData: string | any[],
  commonGroupConfigVO: any,
  contextIndex: string[],
  groupConfigs: any,
  isDeleteBlank: boolean,
  xMap: any[]
) => {
  const contextIndexMap: Record<string, number> = {};
  contextIndex.forEach((item, index) => {
    contextIndexMap[item] = index;
  });
  const { value, seriesIndex, dataIndex } = tooltip?.[0];
  // console.log('seriesIndex, dataIndex', seriesIndex, dataIndex, tooltip);
  const context = chartData?.[seriesIndex]?.[2]?.[dataIndex]?.split(',');
  // contextList: eqpModuleIdIndex loopNoIndex lotIdIndex stationIndex waferIdIndex
  const groupConfig: any = groupConfigs?.find((item: any) => item.sortId === seriesIndex);
  const lot = context[contextIndexMap?.lotId];
  const wafer = context[contextIndexMap?.waferId];
  const recipeId = context[contextIndexMap?.recipeId];
  const eqpId = context[contextIndexMap?.eqpId];
  const chamber = context[contextIndexMap?.chamber];
  const eqpModuleId = context[contextIndexMap?.eqpModuleId];
  const time = isDeleteBlank ? +xMap[value?.[0]] : +value?.[0];

  const tips = [
    `<p style="margin: 0;font-size: 16px;font-weight: 600">Data Details</p>`,
    `<p style="margin: 0;">EQP : ${eqpId || eqpModuleId?.split(':')?.[0]}</p>`,
    `<p style="margin: 0;">Chamber : ${chamber}</p>`,
    `<p style="margin: 0;">Recipe : ${recipeId}</p>`,
    `<p style="margin: 0;">Lot : ${lot}</p>`,
    `<p style="margin: 0;">Wafer : ${wafer?.split(';')?.[0]}</p>`,
    `<p style="margin: 0;">DateTime : ${moment(time).format('YYYY/MM/DD HH:mm:ss.SSS')}</p>`,
    `<p style="margin: 0;">Value : ${value?.[1]}</p>`
  ];
  if (commonGroupConfigVO?.isLoop) {
    tips.splice(6, 0, `<p style="margin: 0;">Loop : ${context[contextIndexMap?.loopNo]}</p>`);
  } else if (commonGroupConfigVO?.stationCompare) {
    tips.splice(
      6,
      0,
      `<p style="margin: 0;">Station : ${context[contextIndexMap?.stationName]}</p>`
    );
  }

  return tips.join('');
};
export interface SummaryOptions {
  chartData: any;
  StatType: string;
  xChartDOM: any;
  width: number;
  height: number;
  commonGroupConfigVO: any;
  xAxisMinMax: number[];
  yAxisMinMax: number[];
  contextIndex: string[];
  chartLegendConfig: NewChartConfig | null;
  isDeleteBlank: boolean;
  xMap: any[];
}
// 修改chartOptions参数
export const getSummaryOptions = ({
  chartData,
  StatType,
  xChartDOM,
  width,
  height,
  commonGroupConfigVO,
  xAxisMinMax,
  yAxisMinMax,
  contextIndex,
  chartLegendConfig,
  isDeleteBlank,
  xMap
}: SummaryOptions): ChartOptions => {
  // 字体放大比例
  const fontSize = chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE;
  const maxYTick = (chartLegendConfig as any)?.yAxisMaxTicksLimit || CM_CHART_DEFUALT_Y_MAX_TICK;
  const ratio = fontSize / CM_CHART_DEFUALT_FONT_SIZE;
  const { data, seriesColor } = chartData;
  const groupConfigs = commonGroupConfigVO?.groupConfigs;
  const xRange = getChartAxisRange(xAxisMinMax?.[0], xAxisMinMax[1], 0.028);
  const yRange = getChartAxisRange(yAxisMinMax?.[0], yAxisMinMax[1], 0.2);
  const options: ChartOptions = {
    title: '',
    type: 'scatter',
    width,
    height,
    background: getBackgroundColor(),
    colors: seriesColor,
    legend: false,
    tooltip: {
      alwaysShowContent: true,
      hideInPointOut: true,
      appendToBody: true
    },
    zoomBar: [],
    zoom: {
      enable: true,
      enableDbClickReset: false,
      enableZoomBackBySelect: true
    },
    select: {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
      enableDelByRightClick: true
    },
    toolbox: {
      markData: {
        tooltip: '',
        icon: '',
        disableCursor: true,
        clickOffset: 20,
        closable: true,
        copyable: true,
        canMoveOutside: true,
        markByIndex: false,
        single: true,
        formatter: (tooltip) =>
          formatTooltipX(
            tooltip,
            data,
            commonGroupConfigVO,
            contextIndex,
            groupConfigs,
            isDeleteBlank,
            xMap
          )
      }
    },
    axes: [
      {
        scale: 'x',
        space: () => 60 + 2 * fontSize,
        stroke: getLabelColor(),
        grid: { show: false },
        border: { show: true, stroke: getLineColor() },
        values: (_: XChart, value: number[]) => {
          return value.map((v) => {
            if (!isDeleteBlank) {
              return `${moment(+v).format('YYYY/MM/DD')}\n${moment(+v).format('HH:mm')}`;
            } else {
              if (xMap[+v]) {
                return `${moment(+xMap[+v]).format('YYYY/MM/DD')}\n${moment(+xMap[+v]).format(
                  'HH:mm'
                )}`;
              } else {
                return '';
              }
            }
          });
        },
        font: `${fontSize}px Nunito Sans`
      },
      {
        side: 3,
        stroke: getLabelColor(),
        grid: { show: false },
        border: { show: true, stroke: getLineColor() },
        drag: false,
        space: dealYSpace(height, maxYTick, fontSize),
        values: (_, val) => val.map((v) => formatterYSM(v, 4)),
        font: `${fontSize}px Nunito Sans`
      }
    ],
    padding: [8 * ratio, 32 * ratio, (9 / 4) * fontSize - 17, 8 * ratio],
    scales: {
      x: {
        time: false,
        range: xRange
      },
      y: {
        range: yRange
      }
    },
    series: [{}, ...getYAxisInfo(data, xChartDOM.value)],
    hooks: {
      beforeDraw: [],
      draw: [],
      setSeries: []
    }
  };
  return options;
};
/**
 * view data table for summary chart
 * @returns table column for summary chart
 */
export const getViewDataTableOptions = () => {
  return VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      tableName: 'common.title.viewData',
      import: false,
      export: true,
      refresh: true // 为了显示 custom;  import, export refresh至少有一个true, 用样式隐藏
    },
    columns: [
      {
        field: 'key',
        title: 'cm.field.dataSet',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'eqp',
        title: 'cm.field.eqp',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'chamber',
        title: 'cm.field.chamber',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'lotId',
        title: 'cm.field.lot',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'wafer',
        title: 'cm.field.wafer',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'startTime',
        title: 'cm.field.startTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'endTime',
        title: 'cm.field.endTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      ...SUMMARY_CHART_STATE_TYPES.map((column) => ({
        field: column,
        title: column,
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      }))
    ]
  });
};
/**
 * 将源数据转换成Summary Chart图标table的数据
 * @param data 源数据
 * @param activeStep 当前选中的工艺步骤
 * @param commonGroupConfigVO selectedAttributeConfigs值的父类
 */
export const getSummaryChartTableData = (
  data: { segmentStatData: any[] },
  activeStep: string,
  commonGroupConfigVO: any,
  contextIndex: string[],
  groupColumns: any[]
) => {
  const gridData = [];
  const filteredData = data?.segmentStatData.filter((s) => {
    if (s.recipestep_name?.includes('_')) {
      return s.recipestep_name?.split('_')?.[0] === activeStep;
    }
    return s.recipestep_name === activeStep;
  });
  const contextIndexMap: Record<string, number> = {};
  contextIndex.forEach((item, index) => {
    contextIndexMap[item] = index;
  });
  for (const segmentData of filteredData) {
    const mateInfo = commonGroupConfigVO?.groupConfigs?.find(
      ({ id }: any) => id === segmentData?.groupConfigId
    );
    const groupCol = groupColumns?.find(
      ({ id }: any) => id === segmentData?.groupConfigId
    );
    const tool = mateInfo?.EQP || '';
    const chamber = mateInfo?.Chamber || '';
    const contextIdx = segmentData.header.indexOf('context');
    const startTimeIdx = segmentData.header.indexOf('START_TIME');
    const endTimeIdx = segmentData.header.indexOf('END_TIME');
    // 关键有用的数据从哪一个小标开始
    const statStartIndex = segmentData.header.indexOf('MIN');
    for (const data of segmentData.valueList) {
      const contexts = data[contextIdx].split(',');
      const gridRow: any = {
        key: groupCol?.name,
        wafer: contexts?.[contextIndexMap?.waferId]?.split(';')[0],
        groupConfigId: segmentData.groupConfigId + '',
        eqp: tool + '',
        chamber: chamber + '',
        lotId: contexts?.[contextIndexMap?.lotId] + '',
        substrateId: contexts?.[contextIndexMap?.waferId] + '',
        startTime: moment.unix(data[startTimeIdx] / 1000).format(CHART_CONSTANT.TraceTimeFormat),
        endTime: moment.unix(data[endTimeIdx] / 1000).format(CHART_CONSTANT.TraceTimeFormat)
      };
      for (let i = statStartIndex; i < data.length; i++) {
        gridRow[segmentData.header[i]] = data[i];
      }
      gridData.push(gridRow);
    }
  }
  return gridData;
};
/**
 * @returns 需要筛选的列，原逻辑是每列都需要
 */
export const getFilterFieldList = () => {
  const columns = getViewDataTableOptions().columns;
  return columns.map((column: any) => column.field);
};
