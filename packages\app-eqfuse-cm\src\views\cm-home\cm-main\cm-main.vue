<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref, watch, shallowRef, computed } from 'vue';
import { LeftOutlined, RightOutlined, DownOutlined, CloseOutlined } from '@ant-design/icons-vue';
import CmDataSetTable from '@/views/cm-home/cm-main/components/cm-data-set-table/cm-data-set-table.vue';
import CmScheduleManageModal from '@/views/cm-home/cm-main/components/cm-schedule-manage/cm-schedule-manage-modal.vue';
import { ReferenceType } from '@/model/reference-type';
import cmModelService from '@/services/cm-model.service';
import { Constants } from '@/model/constants';
import { GroupConfigVO, type TableDataAndColumns } from '@/model/GroupConfigVO';
import {
  showError,
  showWarning,
  showConfirm,
  EesButtonTip,
  successInfo
} from '@futurefab/ui-sdk-ees-basic';
import { CmAnalysisOptions } from '@/model/cm-analysis-options';
import CmResultChart from '@/views/cm-home/cm-main/components/cm-result-chart/index.vue';
import { transferTree, downloadPPTFile, downloadZipFile } from '@/utils';
import type { TrimOriginData, SSEMsgType } from '@/views/cm-configuration/interface';
import CmMultiModeTraceView from './components/cm-overlay-trace-chart/index.vue';
import {
  getLastAnalysis,
  clearResultAnalysis,
  getDynamicReferenceService,
  notifyBackendStartScreenshot,
  getPPTServerConfigInfo,
  recalculatePercentage,
  getSchemaMeta,
  getCustomLegendData
} from '@futurefab/ui-sdk-api';
import { setConfigTable, setAnalysisResult, cancelRunAnalysis } from './config';
import { UUIDUtil } from '@/utils/uuid.util';
import AddScheduleModel from '../add-schedule-model/index.vue';
import type { AddScheduleForm } from '@/utils/schedule';
import {
  useCmstore,
  useBaseStore,
  useCmChartLegendStore,
  useCustomLegendStore
} from '@futurefab/ui-sdk-stores';
import { Splitpanes, Pane } from 'splitpanes';
import { cmMainInfo as buttonIds } from '@/log-config';
import { useRoute, useRouter } from 'vue-router';
import specialTag from '@/views/cm-home/cm-main/components/special-tag/index.vue';
import { t } from '@futurefab/vxe-table';
import ExportconfigurationModal from './components/cm-export-configuration-modal/cm-export-configuration-modal.vue';
import ParametersTrimFilter from '@/views/cm-home/cm-main/parameters-trim-filter/index.vue';
// import { subtract, ceil } from 'lodash-es';

import type { ExportFormData } from './components/cm-export-configuration-modal/interface';
import HistoryList from '@/views/cm-home/cm-main/history-list/index.vue';
import AddDataset from '@/views/cm-home/cm-main/add-dataset/index.vue';
import { useSelfFullScreen } from '@futurefab/ui-sdk-ees-basic';
import { useCmExpend } from '../useCmExpend';
import ReferenceByMetrology from './reference-by-metrology/index.vue';
import { useMetrology } from './useMetrology';
import ViewMetrology from './add-metrology/index.vue';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';

const customLegendStore = useCustomLegendStore();
const legendStroe = useCmChartLegendStore();
const cmStore = useCmstore();
const baseStore = useBaseStore();
// const route = useRoute();
const router = useRouter();

// 公共的SSE连接方法
const createSSEConnection = (
  sseId: string,
  sseServerPath: string,
  options?: {
    onProgress?: (info: any) => void;
    onError?: (data: any) => void;
    onData?: (data: any) => void;
    onComplete?: () => void;
    onOpen?: () => void;
  }
) => {
  const sseUrl = `${sseServerPath}${sseId}`;
  let isReconnecting = false;
  let reconnectCount = 0;
  const maxReconnectAttempts = 5;
  const reconnectDelay = 1000;
  let eventSource: EventSource | null = null;

  const connectEventSource = () => {
    if (reconnectCount >= maxReconnectAttempts) {
      console.error('SSE连接失败：已达到最大重连次数（5次），停止重连');
      if (options?.onError) {
        options.onError({ message: '连接失败，已达到最大重连次数' });
      }
      return;
    }

    eventSource = new EventSource(sseUrl);

    eventSource.onmessage = function (event) {
      const eventData = JSON.parse(event.data);
      const msgType: SSEMsgType = eventData.msgType;
      if (msgType === 'Progress') {
        const { data } = eventData;
        if (options?.onProgress && data) {
          options.onProgress(data.info);
        }
      } else if (msgType === 'Error') {
        const { data } = eventData;
        if (options?.onError) {
          options.onError(data);
        }
      } else if (msgType === 'Data') {
        const { data } = eventData;
        const exportData = data;
        const { ppt, images, progress } = exportData;

        if (progress) {
          if (options?.onProgress) {
            options.onProgress(progress);
          }
        } else if (ppt || images) {
          if (options?.onData) {
            options.onData({ ppt, images });
          }
          if (options?.onComplete) {
            options.onComplete();
          }
        }
      }
    };

    eventSource.onopen = function () {
      reconnectCount = 0;
      if (options?.onOpen) {
        options.onOpen();
      }
      if (options?.onProgress) {
        options.onProgress({ log: 'SSE连接已建立' });
      }
    };

    eventSource.onerror = function (err) {
      console.error('EventSource失败:', err);
      eventSource?.close();

      if (!isReconnecting && reconnectCount < maxReconnectAttempts) {
        isReconnecting = true;
        reconnectCount++;
        console.log(
          `SSE连接断开，${reconnectDelay}ms后进行第${reconnectCount}次重连（最多${maxReconnectAttempts}次）`
        );

        setTimeout(() => {
          isReconnecting = false;
          connectEventSource();
        }, reconnectDelay);
      } else if (reconnectCount >= maxReconnectAttempts) {
        console.error('SSE连接失败：已达到最大重连次数，停止重连');
        if (options?.onError) {
          options.onError({ message: 'SSE连接失败，请检查网络后重试' });
        }
      }
    };
  };

  connectEventSource();

  // 返回关闭连接的方法
  return {
    close: () => {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }
    }
  };
};
const createSSEConnectionWithHandlers = (
  sseId: string,
  sseServerPath: string,
  onConnectionSuccess?: () => void
) => {
  return createSSEConnection(sseId, sseServerPath, {
    onOpen: () => {
      // SSE连接成功建立时的回调
      if (onConnectionSuccess) {
        onConnectionSuccess();
      }
    },
    onProgress: (info) => {
      if (info.status === 'CLOSE' && !info.data.isExportFinish) {
        exportBtnLoading.value = false;
        exportContent.value = '';
        if (!info?.data?.isExportFinish) {
          showError('导出失败');
          // 导出失败时清理 sessionStorage
          sessionStorage.removeItem('cm_sseId');
        }
      } else {
        exportContent.value = `${info.log || info.info.log}`;
      }
    },
    onError: (data) => {
      console.log('SSE错误:', data);
      exportBtnLoading.value = false;
      exportContent.value = '';
      showError('导出失败');
      // 导出错误时清理 sessionStorage
      sessionStorage.removeItem('cm_sseId');
    },
    onData: (data) => {
      const { ppt, images } = data;
      exportContent.value = `下载文件中...`;
      if (ppt) {
        downloadPPTFile(ppt.fileData, ppt.fileName);
      }
      if (images) {
        downloadZipFile(images.fileData, images.fileName);
      }
      exportBtnLoading.value = false;
      successInfo('导出完成');
      // 导出完成时清理 sessionStorage
      sessionStorage.removeItem('cm_sseId');
    },
    onComplete: () => {
      exportBtnLoading.value = false;
      // 导出完成时清理 sessionStorage
      sessionStorage.removeItem('cm_sseId');
    }
  });
};
// contextGroups参数
let groupConfigs: GroupConfigVO[] = [];
let referenceTypes: ReferenceType[] = [];
// 单个group对象
let commonGroupConfigVO: GroupConfigVO = new GroupConfigVO();
let analysisOptions: CmAnalysisOptions = new CmAnalysisOptions();
// const trimConfigModalRef = ref();
const initDInfo = {
  iconFontSize: '18px', // 字体大小
  btnSize: 'small', // 按钮大小
  showContextFilter: false, // 是否展示上下文过滤器弹框
  showMenu: false, // 是否展示菜单
  showExport: false, // 展示导出
  analysisOptions,
  showCharts: false, //  是否展示图表
  columnsOutputNumber: 4,
  parameterOutputNumber: 20, //参数导出
  chartWidth: 300, // 图表宽度，
  groupConfigs,
  selectedReferenceType: 'static', // 数据参考模式
  dynamicReferenceAnalysisId: undefined,
  splitByTool: false,
  splitByChamber: false,
  widgetId: 1,
  manualAnalysis: true,
  datasetConfigurationId: null,
  contextFilter: null,
  isRegexSearch: false,
  showLoading: false,
  loadingTitle: '',
  analysisId: null, // 分析结果id
  resultIdMap: [], // 分析结果集数组
  resultRows: [], // 结果集行
  resultGroups: [], // 结果集行
  showNoAnalysisParam: false, // 是否展示未选中的数据
  useDataNormalization: false, // 是否使用数据标准化
  zeroTicket: false, // 零飘
  showAnalysisSpinner(msg: string, showLoading?: boolean, shouldCancel?: boolean) {
    d.loadingTitle = msg;
    if (showLoading != undefined) {
      d.showLoading = showLoading;
    }
    if (shouldCancel && showLoading === false) {
      cancelRunAnalysis();
    }
  }
};
const isHistory = ref<boolean>(false);
let d = reactive({
  ...initDInfo,
  selectedHeaderColumns: [],
  tableColumns: [] as any,
  tableData: [] as any[],
  commonGroupConfigVO,
  userConfig: {},
  selectedCMCategories: [],
  selectedCMParameterGroups: [],
  selectedCMParameters: [],
  referenceTypes,
  trimRuleList: [] as TrimOriginData[],
  waferData: [] as any[],
  showNoAnalysisParam: false, // 是否展示未选中的数据
  useDataNormalization: false, // 是否使用数据标准化
  zeroTicket: false // 零飘
});
const viewMetrologyRef = ref();
const { hasMetrology, meteroloyWaferData, metrologyViewData, reRunMetrology } = useMetrology(
  isHistory,
  d
);
const viewMetrology = () => {
  viewMetrologyRef.value?.show();
};
const metrologyGroup = async (event: any) => {
  try {
    const res = await getSchemaMeta({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-schema-meta'
      }
    });
    if (res.status === 'SUCCESS') {
      reRunMetrology(d.commonGroupConfigVO, res.data, event.groupMap, () => confirm('clear'));
      const tb = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn();
      d.tableColumns = Object.freeze(tb.tableColumns) as never;
    }
  } catch {}
};
const addScheduleRef = ref();
const canAddSchedule = computed(() => {
  const referenceLen = d.commonGroupConfigVO?.groupConfigs?.filter(
    (item) => item.reference
  )?.length;
  return (
    d.commonGroupConfigVO?.runType === 'DATABASE' &&
    d.commonGroupConfigVO?.fromToType === 'relative' &&
    !d.commonGroupConfigVO.isLoop &&
    !isHistory.value &&
    ((referenceLen && referenceLen !== d.commonGroupConfigVO?.groupConfigs?.length) ||
      referenceType.value !== 'static')
  );
});
const addScheduleInfo = reactive({
  show: () => addScheduleRef.value?.show(),
  addJob: (form: AddScheduleForm) => {
    const groupConfigs = d.commonGroupConfigVO.groupConfigs.map((item: any) => ({
      id: item.id,
      name: item.name,
      reference: item.reference,
      headerInfo: item.headerInfo,
      traceDataQuery: {
        eqpModuleIds: item.eqpModuleIds,
        // recipeId: item.recipeId,
        recipeIds: item.recipeIds,
        loop: false // 只能为false
      }
    }));
    let selectedParams: any[] = [];
    if (d.commonGroupConfigVO.stationCompare) {
      selectedParams = parameter.value?.map((item: any) => item.originalParams).flat();
    } else {
      selectedParams = parameter.value?.map((item: any) => item.parameterName);
    }
    const temp = {
      // similarityThreshold: ceil(subtract(1, d.analysisOptions.similarityThreshold), 1),
      similarityThreshold: d.analysisOptions.similarityThreshold,
      criticalThreshold: d.analysisOptions.criticalThreshold,
      warningThreshold: d.analysisOptions.warningThreshold,
      // 是否使用数据标准化
      useDataNormalization: d.useDataNormalization,
      // 是否展示未选中的数据
      showNoAnalysisParam: d.showNoAnalysisParam,
      // 是否检测非零数据
      detectNonZeroParameter: d.zeroTicket,
      loop: false, // 只能为false
      trimRules: d.trimRuleList,
      selectedParams,
      stationCompare: d.commonGroupConfigVO.stationCompare, //true or false
      timePeriod: {
        relativeTime: d.commonGroupConfigVO.relativeTime,
        relativeUnit: d.commonGroupConfigVO.relativeUnit
      },
      alarmNotification: {
        emailNotification: form.emailNotification,
        alarmConditions: form.emailNotification ? form.alarmConditions : [],
        notificationRecipients: form.emailNotification ? form.notificationRecipients : []
      },
      selectedSteps: stepCheck.value?.map((item: any) => item.recipeStepId)
    };
    addScheduleRef.value?.addJob({ groupConfigs, ...temp }, form);
  }
});
const scheduleManageRef = ref();
const scheduleManageInfo = reactive({
  show: () => scheduleManageRef.value?.show(),
  reloadManage: () => scheduleManageRef.value?.reloadManage()
});
// run info
const runMode = shallowRef({
  // example: FDC DB
  runTypeText: '',
  // example: Wafer
  runModeText: '',
  // example: Static
  runByText: ''
});
const setRunMode = (value: any) => {
  runMode.value = { ...runMode.value, ...value };
};
// filter model参数
const categoryCheckData = shallowRef([]);
const parameter = shallowRef([]);
const beforeRunStepCheck = shallowRef<string[]>([]); // 上一次 run 的
const stepCheck = shallowRef([]); // 选了 但可能还没有run的数据
const allCategory = shallowRef([]);
const paramsTree = shallowRef([]);
const allSteps = shallowRef([]);
const tableAndChartRef = ref();
const tableRef = ref();
const firstColWidth = ref<number>(350);
const { cmSplitData, toLeft, toRight } = useCmExpend(tableRef, tableAndChartRef, firstColWidth, d);
const setParamsTreeAllSteps = (data: any) => {
  allCategory.value = data.allCategory;
  paramsTree.value = data.paramsTree;
  allSteps.value = data.allSteps;
};
let sseServerPath = '';
onMounted(async () => {
  if (d.commonGroupConfigVO.attributeConfigs.length == 0) {
    // 初始化配置信息
    d.commonGroupConfigVO.initAttributesAndConfigsFromServer().then(() => {
      cmModelService.getDatasetConfigurationByUserId().then(async (response) => {
        if (response) {
          d.userConfig = response;
          d.datasetConfigurationId = (d.userConfig as any)?.datasetConfigurationId;
          await d.commonGroupConfigVO.setSelectedConfigsByNameArray(
            response.selectedAttributes?.map((e: { name: any }) => e.name)
          );
        }
      });
    });
  }
  cmModelService.getReferenceTypes().then((data) => {
    d.referenceTypes = data;
  });

  // 获取ppt server配置信息
  const { data: pptServerConfig } = await getPPTServerConfigInfo({ bodyParams: {} });
  const { sseHost, ssePort } = pptServerConfig;
  sseServerPath = `${sseHost}:${ssePort}/sse/`;

  // 如果有未完成导出任务，则重新连接SSE
  const storedSseId = sessionStorage.getItem('cm_sseId');
  if (storedSseId) {
    retryConnectSSE(storedSseId);
  }
  // 初始化默认选中的行
  initDefaultClickedRow();
});

const retryConnectSSE = async (storedSseId: string) => {
  if (storedSseId && sseServerPath) {
    sseId = storedSseId;
    console.log('尝试重新连接SSE:', sseServerPath + sseId);

    try {
      currentSSEConnection = createSSEConnectionWithHandlers(sseId, sseServerPath, () => {
        exportBtnLoading.value = true;
      });
    } catch (error) {
      console.error('重新连接SSE失败:', error);
      sessionStorage.removeItem('cm_sseId');
      exportBtnLoading.value = false;
    }
  }
};
// 修改History
const changeIsHistory = (value: boolean) => {
  isHistory.value = value;
};
// 初始化filter弹窗，从而设置filter参数
const setParameter = (datasetEntry: 'add' | 'modify', request?: any) => {
  if (!request && datasetEntry === 'modify') {
    filterParametersModalRef.value?.initModalData(datasetEntry, {
      allCategory: allCategory.value,
      paramsTree: paramsTree.value,
      allSteps: allSteps.value,
      trimRules: d.trimRuleList
    });
    return;
  }
  filterParametersModalRef.value?.initModalData(datasetEntry, request);
};
// 获取run参数
const getRunParams = (isHistory?: boolean, type?: string) => {
  let selectedParams: any[] = [];
  if (d.commonGroupConfigVO.stationCompare) {
    // 别名二维数组转换为一维数组
    selectedParams = parameter.value?.map((item: any) => item.originalParams).flat();
  } else {
    selectedParams = parameter.value?.map((item: any) => item.parameterName);
  }
  const convertedGroupConfigs = d.commonGroupConfigVO.groupConfigs.map((gc) =>
    gc.getRequestInstance()
  );
  const analysisOptionParams = {
    criticalThreshold: d.analysisOptions.criticalThreshold,
    similarityThreshold: d.analysisOptions.similarityThreshold,
    loop: d.commonGroupConfigVO.isLoop,
    stationCompare: d.commonGroupConfigVO.stationCompare,
    warningThreshold: d.analysisOptions.warningThreshold
  };
  if (analysisOptionParams.loop) {
    convertedGroupConfigs.forEach((item: any) => {
      item.waferLoops.stepIds = stepCheck.value;
    });
  }
  const params = {
    ...analysisOptionParams,
    runMode: runMode.value,
    groupConfigs: convertedGroupConfigs,
    useDataNormalization: d.useDataNormalization,
    fileNames: d.commonGroupConfigVO.fileNames!,
    trimRules: d.trimRuleList,
    runType: d.commonGroupConfigVO.runType,
    selectedParams,
    selectedSteps: stepCheck.value,
    showNoAnalysisParam: d.showNoAnalysisParam,
    detectNonZeroParameter: d.zeroTicket,
    allCategory: allCategory.value,
    paramsTree: paramsTree.value,
    allSteps: allSteps.value
  };
  if (isHistory) {
    return {
      ...params,
      historyRequestId: websocketParams.value?.historyRequestId || websocketParams.value?.requestId,
      historyRequestDt: websocketParams.value?.historyRequestDt || websocketParams.value?.requestDt
    };
  }
  if (type === 'analysisOption') {
    return {
      ...analysisOptionParams,
      requestId: websocketParams.value?.requestId,
      requestDt: websocketParams.value?.requestDt
    };
  }
  return { ...params };
};
// 构造表格内容,并加载图表
const handleRunAnalysis = async (result: any, oldConfigColor?: any[]) => {
  d.commonGroupConfigVO.resultGroups = result?.resultMap;
  d.commonGroupConfigVO.analysisOptions = d.analysisOptions;
  let tb = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn();
  d.tableData = tb.tableData;
  d.tableColumns = Object.freeze(tb.tableColumns) as never;
  if (oldConfigColor && oldConfigColor.length > 0) {
    d?.commonGroupConfigVO?.groupConfigs.forEach((item: any) => {
      const oldItem = oldConfigColor.find((old: any) => old.id === item.id);
      item.color = oldItem?.color;
      item.chartLegendColor = oldItem?.color;
    });
  }
  // 分析完成后，初始化默认选中的行
  initDefaultClickedRow();
  if (result?.requestId) {
    // 发送websocket
    websocketParams.value = {
      loop: d.commonGroupConfigVO.isLoop,
      historyRequestDt: result?.historyRequestDt,
      historyRequestId: result?.historyRequestId,
      requestDt: result?.requestDt,
      requestId: result?.requestId,
      descriptorValues: d.commonGroupConfigVO.getSelectedDescriptorValues(),
      paramAliasList: [tb.tableData.slice(3, 4)?.[0]?.parameterName],
      groupResultIDs: d.resultIdMap.map((m: any) => [String(m.groupConfigId), m.resultRequestId]),
      recipeStepsForScore: [],
      useSampling: true,
      useDataNormalization: d.useDataNormalization,
      runType: d.commonGroupConfigVO.runType
    };
    sendWebSocket(websocketParams.value);
    // 展开图表
    toggleCharts(true);
    // 更新路由参数为空，表示非历史分析
    router.replace({ query: {} });
  }
};
const handleResetAnalysisOptions = () => {
  d.analysisOptions.resetTempOptions();
};
const handleApplyAnalysisOptions = async () => {
  d.analysisOptions.applyAnalysisOption();
  d.showMenu = false;
  if (d.tableData.length > 3) {
    // 数据量大的时候需要loading
    d.showAnalysisSpinner('Preparing data...', true);
    const analysisRes = await recalculatePercentage({
      bodyParams: getRunParams(false, 'analysisOption'),
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'recalculate-percentage'
      }
    });
    if (analysisRes?.status === 'SUCCESS') {
      const oldConfigColor = d?.commonGroupConfigVO?.groupConfigs.map((item: any) => ({
        id: item.id,
        name: item.name,
        color: item.color
      }));
      d.showAnalysisSpinner('Preparing data...', false);
      handleRunAnalysis(analysisRes.data, oldConfigColor);
    } else {
      d.showAnalysisSpinner('Preparing data...', false);
    }
  }
};
const handleShowMenu = (visible: boolean) => {
  d.analysisOptions.closeAnalysisOption(visible);
};
const filterParametersModalRef = ref();
const cmDataSetTable = ref();
let confirm = async (type: string, isCheck?: boolean, selectedReferenceType?: string) => {
  if (!isHistory.value) websocketParams.value = {};
  switch (true) {
    case type === 'clear':
      referenceTypeValue.value = '';
      setRunMode({ runByText: '' });
      d.showCharts = false;
      if (selectedReferenceType) {
        Object.assign(d, { ...initDInfo, selectedReferenceType });
      } else {
        Object.assign(d, initDInfo);
      }
      d.commonGroupConfigVO.clearResult();
      // 清除分析结果
      if (d.tableData && d.tableData.length > 1) {
        d.tableData?.splice(1, d.tableData.length - 1);
      }
      d.tableData.push({
        name: 'ParameterAndRecipe',
        id: 'ParameterAndRecipe',
        parentId: null,
        showMatchingInfo: false,
        matchingInfo: {
          unmatchingCountAll: 0,
          warningCountAll: 0,
          matchingCountAll: 0,
          missingCountAll: 0,
          totalCountAll: 0
        },
        parentGroupId: null,
        children: []
      });
      d.tableColumns?.forEach((item: any, i: number) => {
        if (i > 0) {
          item.reference = false;
          item.resultGroupInfo = undefined;
        }
      });
      cmResultChart.value.clearAllChartData();
      break;

    case type === 'delete':
      Object.assign(d, initDInfo);
      d.tableColumns = [];
      d.tableData = [];
      if (!isCheck) {
        d.commonGroupConfigVO.clear();
        datasetModal.value.clearModalAllData();
        // 默认清除runInfo
        referenceTypeValue.value = '';
        runMode.value = {
          runTypeText: '',
          runModeText: '',
          runByText: ''
        };
      } else {
        // 弹窗所需清除的数据
        d.commonGroupConfigVO.clearResult();
      }
      // clearTrimming();
      filterParametersModalRef.value.clearFilterParameter();
      cmResultChart.value.clearAllChartData();
      break;
  }
  // 清除最后一次的分析结果
  await clearResultAnalysis({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'clear-result-analysis'
    }
  });
  // 清除customLegendStore数据
  customLegendStore.resetCustomLegendData();
};

let toggleCharts = (b: boolean) => {
  d.showCharts = b;
};

let createNewGroupConfigs = () => {
  let baseDataset: any = {}; // 构造请求参数
  baseDataset.timePeriod = d.commonGroupConfigVO.getTimePeriod(false);
  baseDataset.fromToType = d.commonGroupConfigVO.fromToType;
  baseDataset.selection = d.commonGroupConfigVO.getDataSplitSelection();
  baseDataset.selectedAttributes = d.commonGroupConfigVO.selectedAttributeConfigs;
  let splitAttributeNames = d.commonGroupConfigVO.createDatasetByEachChamber
    ? [Constants.EQUIPMENT, Constants.CHAMBER]
    : [Constants.EQUIPMENT];
  let req = { baseDataset, splitAttributeNames };
  cmModelService.getSplitDatasetData(req).then((res: any) => {
    let tb: TableDataAndColumns = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn(
      res.data
    );
    let tableData = tb.tableData;
    let tableColumns = tb.tableColumns;
    d.tableData = tableData;
    d.tableColumns = Object.freeze(tableColumns) as never;
  });
};
// dataset modal
const datasetModal = ref();
const showDataset = ref<boolean>(false);
const showCache = ref<boolean>(false);
const datasetEntry = ref<'add' | 'modify'>('add');
const showAddDataset = (entry?: 'add' | 'modify', cache?: boolean) => {
  datasetEntry.value = entry || 'add';
  showCache.value = cache || false;
  showDataset.value = !showDataset.value;
};
let renderNewGroupConfigs = (commonGroupConfigVO: any) => {
  // 设定run的模式为static
  referenceType.value = 'static';
  referenceTypeValue.value = 'Static';
  // 赋值表格
  d.commonGroupConfigVO = commonGroupConfigVO;
  // console.log('新的渲染d.commonGroupConfigVO:', d.commonGroupConfigVO);
  d.columnsOutputNumber = d.commonGroupConfigVO?.groupConfigs.length || 0;
  renderTable();

  // 新的组配置渲染完成后，初始化默认选中的行
  initDefaultClickedRow();
};
let renderTable = () => {
  let tb = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn();
  d.tableData = tb.tableData as never;
  d.tableColumns = tb.tableColumns as never;

  // 表格渲染完成后，初始化默认选中的行
  initDefaultClickedRow();
};

// chart dom
const cmResultChart = ref();
const websocketParams = ref<any>({});
const unMatchStepInfo = ref({});
const sendWebSocket = (params: any, row?: any) => {
  d.showCharts = true;
  if (row) {
    unMatchStepInfo.value = row;
    if (params?.paramAliasList !== websocketParams.value?.paramAliasList) {
      websocketParams.value = { ...websocketParams.value, ...params };
      const paramAliasList = websocketParams.value?.paramAliasList?.filter((item: any) => !!item);
      if (row?.parameterName !== 'Ungrouped' && paramAliasList && paramAliasList.length > 0) {
        cmResultChart.value?.handleSendSocket(websocketParams.value);
      }
    }
    // TODO 选择分组：多条数据，并展示分页
  } else {
    unMatchStepInfo.value = {};
    websocketParams.value = { ...websocketParams.value, ...params };
    const paramAliasList = websocketParams.value?.paramAliasList?.filter((item: any) => !!item);
    if (paramAliasList && paramAliasList.length > 0) {
      cmResultChart.value?.handleSendSocket(websocketParams.value);
    }
  }
};
const handleOverlaySendSocket = (params: any) => {
  cmResultChart.value.handleOverlaySendSocket(params);
};
const sendOverlayData = (data: any) => {
  chamberMatchingChartModelRef.value.sendOverlayData(data);
};
let cancelRequest = () => {
  confirm('clear');
  cancelRunAnalysis();
  handleCancelRunAnalysis();
};
// 创建一个 ref 来存储 AbortController
const abortController = ref<AbortController | null>(null);
// 取消 runAnalysis 的方法
const handleCancelRunAnalysis = () => {
  if (abortController.value) {
    abortController.value.abort();
    abortController.value = null;
  }
};
const referenceByMetrologyRef = ref();
// run type check
const handleRunMenuClick = async (runType: any) => {
  referenceType.value = runType?.key;
  referenceTypeValue.value = runType?.item?.headerText;
  setRunMode({ runByText: runType?.item?.headerText });
  if (runType.key === 'reference_metrology') {
    const referenceList = d.tableColumns
      ?.filter((item: any) => item.reference)
      .map((item: any) => item.id);
    const res = await referenceByMetrologyRef.value.show(referenceList);
    if (res && res.length) {
      d.tableColumns.forEach((item: any) => {
        if (item && item.id) {
          item.reference = res.includes(item.id);
        }
      });
    } else {
      return;
    }
  }
  runAnalysis();
  legendStroe.SET_CM_CHART_LEGEND_CONFIG(null);
};
// 点击运行触发
const runAnalysis = async () => {
  try {
    // 创建一个新的 AbortController 实例
    abortController.value = new AbortController();
    const { signal } = abortController.value;
    toggleCharts(false);
    // 清除分析结果
    cmResultChart.value.clearAllChartData();
    cmDataSetTable.value.clearModalTemp();
    chamberMatchingChartModelRef.value?.clear();
    await d.commonGroupConfigVO.isAnalysisSettingInvalid(referenceType.value);
    if (d.commonGroupConfigVO.isDynamicReferenceByTrace(referenceType.value)) {
      d.showAnalysisSpinner('Preparing data...', true);
      const params = getRunParams(isHistory.value);
      // 获取reference内容
      const referenceIds = await getDynamicReferenceService({
        bodyParams: params,
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'get-dynamic-reference'
        }
      });
      if (referenceIds?.status !== 'SUCCESS') {
        d.showAnalysisSpinner('Preparing data...', false, false);
        return;
      }
      d.commonGroupConfigVO.groupConfigs?.forEach((item: any) => {
        if (referenceIds?.data?.includes(String(item?.id))) {
          item.reference = true;
        } else {
          item.reference = false;
        }
      });
    }
    // 开始分析
    const runParams = getRunParams(isHistory.value);
    const result: any = await cmModelService.analyze(
      runParams,
      d.commonGroupConfigVO.groupConfigs,
      d.dynamicReferenceAnalysisId!,
      d,
      d.commonGroupConfigVO.isDynamicReferenceByTrace(referenceType.value)
    );
    if (result?.requestId) {
      // 重置custom legend
      customLegendStore.resetCustomLegendData();
    }
    handleRunAnalysis(result);
    beforeRunStepCheck.value = [...stepCheck.value];
  } catch (err) {
    d.showLoading = false;
    if (err instanceof Error) {
      showError(err.message);
    }
    d.showAnalysisSpinner('Analyzing...', false, false);
  } finally {
    d.showLoading = false;
    d.showAnalysisSpinner('Analyzing...', false, false);
  }
};
// Chamber Matching Chart
const chamberMatchingChartModelRef = ref();
const showChamberMatchingChartModel = () => {
  if (d.showCharts || (d.tableData && d.tableData.length > 3)) {
    chamberMatchingChartModelRef.value.show();
  } else {
    showWarning('It can be used after analysis is complete.', false);
  }
};
// function clearTrimming() {
//   d.trimRuleList = [];
//   trimConfigModalRef.value?.refresh();
// }
// watch(
//   () => route.path,
//   (val: string) => {
//     if (val.includes('/cm-main') && !isHistory.value) {
//       clearTrimming();
//     }
//   },
//   { immediate: false }
// );
const stepInfo: any = ref([]);
const referenceType = ref<string>('static');
const referenceTypeValue = ref<string>('');
// 运行前筛选
const changeParametersTrimFilter = (event: any) => {
  // 选中的参数
  parameter.value = event.parameter;
  // 选中的步骤
  stepCheck.value = event.stepCheck;
  // 选中的trim
  d.trimRuleList = event.trimRuleList;
  // 选中的showUnSelected
  d.showNoAnalysisParam = event.showNoAnalysisParam;
  // 零飘
  d.zeroTicket = event.zeroTicket;
  // useDataNormalization
  d.useDataNormalization = event.normalization;
};
// 设置filter弹窗参数
const setFilterModelParams = (params: any) => {
  stepCheck.value = params.selectedSteps;
  beforeRunStepCheck.value = [...params.selectedSteps];
  parameter.value = params.selectedParams?.map((item: any) => {
    return {
      parameterName: item,
      originalParams: [item]
    };
  });
};
// 获取历史分析结果
const getLastAnalysisResult = async (historyInfo?: any) => {
  d.showAnalysisSpinner('Preparing data...', true);
  let params = {};
  if (historyInfo) {
    params = historyInfo;
  } else {
    const urlParams = new URLSearchParams(window.location.search);
    const requestDt = urlParams.get('requestDt');
    const requestId = urlParams.get('requestId');
    if (requestDt && requestId) {
      params = {
        requestDt,
        requestId
      };
    }
  }

  const lastAnalysis = await getLastAnalysis({
    bodyParams: params,
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-last-analysis'
    }
  });
  if (lastAnalysis.status === 'SUCCESS') {
    isHistory.value = true;
    const { request, result } = lastAnalysis.data;
    if (!request || !result) {
      handleResetAnalysisOptions();
      d.showAnalysisSpinner('Preparing data...', false);
      return;
    }
    // 赋值runMode、allSteps、paramsTree
    runMode.value = request.runMode;
    setParameter('modify', request);
    setParamsTreeAllSteps({
      allCategory: request.allCategory,
      paramsTree: request.paramsTree,
      allSteps: request.allSteps
    });
    // 构建入参
    await setConfigTable(request, d, renderNewGroupConfigs);
    // 构建分析结果
    await setAnalysisResult(result, d, websocketParams, sendWebSocket, toggleCharts, request);
    // filter弹窗参数设置
    setFilterModelParams({
      selectedParams: request.selectedParams,
      selectedSteps: request.selectedSteps
    });
    // 读取customLegendData
    const customLegendRes = await getCustomLegendData({
      bodyParams: {
        requestId: result.requestId,
        requestDt: result.requestDt
      }
    });
    // 存储customLegendData
    if (customLegendRes.status === 'SUCCESS' && customLegendRes.data?.length > 0) {
      customLegendStore.setCustomLegendData({
        legendByCustom: customLegendRes?.data?.[0]?.infoJson?.legendByCustom,
        legendBy: customLegendRes?.data?.[0]?.infoJson?.legendBy,
        lineItems: customLegendRes?.data?.map((item: any) => ({
          ...item.infoJson.lineItems?.[0],
          ctxExtendedId: item.ctxExtendedId
        }))
      });
    }
    // 获取历史分析结果后，初始化默认选中的行
    await initDefaultClickedRow();
  } else {
    handleResetAnalysisOptions();
    d.showAnalysisSpinner('Preparing data...', false);
  }
};

const isLocal = computed(() => cmStore.smartMachineLocalMode);
onMounted(async () => {
  await getLastAnalysisResult();
});

// ScreenShot
let exportBtnLoading = ref(false);
let sseId = '';
let exportConfig = reactive<any>({});
const treeData = ref<any[]>([]);
const exportconfigurationModalRef = ref<any>(null);
let currentSSEConnection: { close: () => void } | null = null;
let clickedRow: any = null;
const getClickedRow = (rowData: any) => {
  clickedRow = rowData.row;
};

// 初始化默认选中的行
const initDefaultClickedRow = () => {
  if (d.tableData && d.tableData.length > 0) {
    // 获取第一个 group 下的第一个 child
    const firstGroup = d.tableData.find(
      (item: any, index: number) =>
        index >= 2 &&
        item.groupId == null &&
        item.parameterName &&
        item.parameterName !== 'Ungrouped'
    );

    if (firstGroup) {
      // 查找该 group 下的第一个 child
      const firstChild = d.tableData.find((item: any) => item.groupId === firstGroup.parentGroupId);

      if (firstChild) {
        clickedRow = firstChild;
      } else {
        clickedRow = firstGroup;
      }
    }
  }
};
const openExportConfigurationModal = async () => {
  treeData.value = transferTree(d.tableData);
  if (!cmDataSetTable.value || !treeData.value.length) {
    showError('没有可导出的数据！');
    return;
  }

  // 打开弹窗并传递选中的行数据
  exportconfigurationModalRef.value?.open();
};

// 导出弹窗点击确认
const handleExportConfigurationConfirm = async (exportFormData: ExportFormData) => {
  exportConfig = exportFormData;

  // 如果选择了custom legend，将cm-main中的custom legend设置添加到配置中
  // if (exportFormData.exportFilter.legendType === 'custom') {
  //   exportConfig.customLegendData = {
  //     legendByCustom: customLegendStore.customLegendData.legendByCustom,
  //     legendBy: customLegendStore.customLegendData.legendBy,
  //     lineItems: customLegendStore.customLegendData.lineItems
  //   };
  // }

  const requestId = websocketParams.value?.requestId;
  const requestDt = websocketParams.value?.requestDt;
  sseId = UUIDUtil.new();
  // 存储 sseId 到 sessionStorage，用于页面刷新后重连
  sessionStorage.setItem('cm_sseId', sseId);

  currentSSEConnection = createSSEConnectionWithHandlers(sseId, sseServerPath, () => {
    exportBtnLoading.value = true;
  });

  // 新开页签进行截图
  const currentOrigin = window.location.origin;
  const token = localStorage.getItem('token') || '';
  const url = `${currentOrigin}/cm/cm-screenshot?sseId=${sseId}&token=${token}&isScreenshot=true&config=${encodeURIComponent(JSON.stringify(exportConfig))}&requestId=${requestId}&requestDt=${requestDt}`;

  console.log(`### screenshot url: ${url}`);

  // const debugRequest = async (url: string) => {
  //   const response = await fetch('http://10.10.2.191:3000/debug', {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json'
  //     },
  //     body: JSON.stringify({
  //       url: url,
  //       options: {
  //         waitTime: 3000
  //       }
  //     })
  //   });
  //   return response.json();
  // };
  // const debugRes = await debugRequest(url);
  // console.log('### debugRes', debugRes);
  //  window.open(url, '_blank');
  notifyBackendStartScreenshot({ bodyParams: { url } }).then((res) => {
    console.log(res);
  });
};

let exportContent = ref('Exporting...');

// 全屏
const cmMainRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(cmMainRef as any);

const changeColumnNameColor = (event: any) => {
  const changeNameItems = { old: [] as string[], new: [] as string[] };
  [...(d.tableColumns || []), ...(d.commonGroupConfigVO?.groupConfigs || [])].forEach(
    (item: any) => {
      if (item.id && event[item.id + '']) {
        const temp = event[item.id + ''];
        if (temp.groupNameAlias !== item.name) {
          // 修改过的 name
          changeNameItems.old.push(item.name);
          changeNameItems.new.push(temp.groupNameAlias);
          // 同步到 新增 dataset 的 弹窗中
          datasetGroupStore.handleRename(
            changeNameItems.old!,
            changeNameItems.new,
            true,
            isHistory.value
          );
        }
        item.key = temp.groupNameAlias;
        item.name = temp.groupNameAlias;
        item.color = temp.groupColor;
        item.chartLegendColor = temp.groupColor;
      }
    }
  );
  if (d.tableColumns) {
    d.tableColumns = [...d.tableColumns];
  }
};
const nameColorChange = () => {
  if (d.tableColumns) {
    d.tableColumns = [...d.tableColumns];
  }

  if (d.commonGroupConfigVO?.groupConfigs && d.tableColumns) {
    d.commonGroupConfigVO.groupConfigs.forEach((groupConfig: any, index: number) => {
      const tableColumn = d.tableColumns[index + 1];
      if (tableColumn) {
        groupConfig.name = tableColumn.key;
        groupConfig.color = tableColumn.color;
        groupConfig.chartLegendColor = tableColumn.chartLegendColor;
      }
    });
    d.commonGroupConfigVO.groupConfigs = [...d.commonGroupConfigVO.groupConfigs];
  }
};
onUnmounted(() => {
  if (currentSSEConnection) {
    currentSSEConnection.close();
    currentSSEConnection = null;
    //sessionStorage.removeItem('cm_sseId');
  }
});
</script>

<template>
  <div
    ref="cmMainRef"
    v-isLoading="{
      isShow: d.showLoading,
      hasButton: true,
      title: d.loadingTitle,
      buttonEvent: cancelRequest
    }"
    class="cm-main-box"
  >
    <div class="cm-main-box-all-btn">
      <a-space align="center" :size="0">
        <i class="iconfont icon-navibar-eq-cm-main" style="color: var(--bg-icon-active-color)" />
        <span class="cm-main-box-all-btn-title">{{ t('cm.title.cmMain') }}</span>
        <vxe-tooltip
          :content="t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
          :use-h-t-m-l="true"
          :theme="baseStore.theme"
        >
          <i
            v-if="cmMainRef"
            class="iconfont chart_icon_icon zoom-icon fullscreen-icon"
            :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
            @click="toggle"
          ></i>
        </vxe-tooltip>
        <div class="cm-main-box-all-btn-run-info">
          <a-tooltip placement="top" v-if="runMode?.runTypeText">
            <template #title>
              <span>{{ t('cm.label.dataSource') }}: {{ runMode.runTypeText }}</span>
            </template>
            <span class="cm-main-box-all-btn-run-info-span" v-if="runMode?.runTypeText">
              {{ runMode.runTypeText }}
            </span>
          </a-tooltip>
          <a-tooltip placement="top" v-if="runMode?.runModeText">
            <template #title>
              <span>{{ t('cm.title.runBy') }}: {{ runMode.runModeText }}</span>
            </template>
            <span class="cm-main-box-all-btn-run-info-span" v-if="runMode?.runModeText">
              {{ runMode.runModeText }}
            </span>
          </a-tooltip>
          <a-tooltip placement="top" v-if="runMode?.runByText">
            <template #title>
              <span>{{ t('cm.title.reference') }}: {{ runMode.runByText }}</span>
            </template>
            <span class="cm-main-box-all-btn-run-info-span" v-if="runMode?.runByText">
              {{ runMode.runByText }}
            </span>
          </a-tooltip>
        </div>
      </a-space>
      <a-space align="center" class="cm-main-box-all-btn-right" :size="0">
        <ees-button-tip
          :marginRight="10"
          :icon="'#icon-btn-add'"
          :text="t('cm.btn.addDataset')"
          type="svg"
          is-border
          @on-click="
            () => {
              showAddDataset('add');
            }
          "
        />
        <EesButtonTip
          :marginRight="10"
          is-border
          :icon="'#icon-btn-input-metrology-data'"
          :text="'MET'"
          :disabled="d.tableColumns?.length < 2 || d.commonGroupConfigVO.isLoop"
          @click="viewMetrology"
        />
        <!-- 新筛选 -->
        <ParametersTrimFilter
          ref="filterParametersModalRef"
          v-model:category-check-data="categoryCheckData"
          v-model:parameter="parameter"
          v-model:step-check="stepCheck"
          :dataInfo="d"
          :isHistory="isHistory"
          @changeParametersTrimFilter="changeParametersTrimFilter"
          @setParamsTreeAllSteps="setParamsTreeAllSteps"
          @show-loading="() => d.showAnalysisSpinner('Preparing data...', true)"
          @close-loading="() => d.showAnalysisSpinner('Preparing data...', false)"
        />
        <!-- run analysis -->
        <a-dropdown :placement="'bottom'" arrow>
          <template #overlay>
            <a-menu @click="handleRunMenuClick">
              <p style="padding: 5px 14px; font-weight: bold; color: var(--text-title-color)">
                {{ t('cm.title.runBy') }}
              </p>
              <a-menu-divider />
              <a-menu-item
                v-for="typeInfo in d.referenceTypes"
                :key="typeInfo.key"
                :value="typeInfo.value"
                :headerText="typeInfo.headerText"
                :disabled="typeInfo.disable"
              >
                <a-tooltip placement="right" v-if="typeInfo.disable">
                  <template #title>
                    <span>{{ t('metrology.tip.noMetrologyData') }}</span>
                  </template>
                  <span style="display: inline-block; width: 100%">{{ typeInfo.value }}</span>
                </a-tooltip>
                <span v-else>{{ typeInfo.value }}</span>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button type="primary">
            {{ t('cm.btn.run') }}
            <DownOutlined />
          </a-button>
        </a-dropdown>
        <a-divider
          type="vertical"
          style="width: 1px; height: 18px; margin: 0 10px; color: var(--bg-hover-2-color)"
        />
        <ees-button-tip
          is-border
          :marginRight="10"
          icon="#icon-btn-overlay-trace-chart"
          :text="t('Overlay Trace Chart')"
          @on-click="showChamberMatchingChartModel()"
        />
        <ees-button-tip
          v-if="!exportBtnLoading"
          is-border
          :marginRight="10"
          :disabled="!websocketParams?.requestId"
          icon="#icon-btn-export"
          :text="t('common.btn.exportNew')"
          @click="openExportConfigurationModal()"
        />
        <vxe-tooltip
          :content="exportContent"
          trigger="hover"
          :theme="'light'"
          :enterable="true"
          :enter-delay="0"
          :leave-delay="0"
          v-if="exportBtnLoading"
        >
          <div class="export-loading-btn">
            <div class="export-loading--spinner"></div>
          </div>
        </vxe-tooltip>
        <ees-button-tip
          is-border
          :marginRight="10"
          icon="#icon-btn-clear"
          :text="$t('cm.btn.clearResults')"
          @click="
            showConfirm({ msg: `${$t('cm.tips.clearResult')}` }).then((type: string) => {
              if (type !== 'confirm') return;
              confirm('clear');
            })
          "
        />
        <ees-button-tip
          is-border
          :marginRight="10"
          icon="#icon-btn-delete"
          :text="$t('cm.btn.deleteResults')"
          @click="
            showConfirm({ msg: `${$t('cm.tips.deleteResults')}` }).then((type: string) => {
              if (type !== 'confirm') return;
              confirm('delete');
            })
          "
        />
        <ees-button-tip
          v-if="!isLocal"
          is-border
          :marginRight="10"
          icon="#icon-btn-schedule-job-management"
          :text="t('cm.title.scheduleManage')"
          @on-click="scheduleManageInfo.show"
        />
        <!-- 历史分析记录 -->
        <HistoryList />
      </a-space>
    </div>
    <div ref="tableAndChartRef" class="cm-main-box-table-chart-warp">
      <splitpanes class="default-theme">
        <Pane :size="cmSplitData.paneLeftWith">
          <div ref="tableRef" class="cm-main-box-table-warp">
            <cm-data-set-table
              ref="cmDataSetTable"
              v-model:common-group-config="d.commonGroupConfigVO"
              v-model:table-data="d.tableData"
              v-model:columns="d.tableColumns"
              v-model:step-info="stepInfo"
              v-model:firstColWidth="firstColWidth"
              :selected-reference-type="referenceType"
              :use-data-normalization="d.useDataNormalization"
              :is-history="isHistory"
              :websocketParams="websocketParams"
              @send-websocket="sendWebSocket"
              @getClickedRow="getClickedRow"
              @nameColorChange="nameColorChange"
            >
              <template #dataset-header-right>
                <div class="cm-header-tag-btn">
                  <a class="cm-header-tag-btn-item" @click="showAddDataset('modify')">
                    <i class="iconfont icon-eq-modify-dataset" />
                  </a>
                  <a-dropdown
                    v-model:open="d.showMenu"
                    :size="d.btnSize"
                    trigger="click"
                    @open-change="handleShowMenu"
                  >
                    <template #overlay>
                      <a-menu>
                        <div
                          style="display: flex; justify-content: space-between; padding: 6px 10px"
                        >
                          <p
                            style="
                              font-size: 16px;
                              font-weight: bold;
                              line-height: 22px;
                              color: var(--text-title-color);
                            "
                          >
                            {{ t('cm.label.analysisOptions') }}
                          </p>
                          <CloseOutlined @click="d.showMenu = false" />
                        </div>
                        <a-menu-divider />
                        <a-menu-item key="1">
                          <a-space direction="vertical">
                            <div class="menu-item-text">
                              {{ t('cm.title.datasetMatchRange') }} (%)
                            </div>
                            <div class="special-tag-box">
                              <special-tag :type="'error'" :text="t('cm.label.critical')" />
                              <a-space :size="5">
                                <a-input-number value="0" disabled />
                                ~
                                <a-input-number
                                  v-model:value="d.analysisOptions.tempOptions.criticalThreshold"
                                  :min="1"
                                  disabled
                                />
                              </a-space>
                            </div>
                            <div class="special-tag-box">
                              <special-tag :type="'warning'" :text="t('cm.label.warning')" />
                              <a-space :size="5">
                                <a-input-number
                                  v-model:value="d.analysisOptions.tempOptions.criticalThreshold"
                                  :disabled="false"
                                  :min="1"
                                  :max="
                                    Number(d.analysisOptions.tempOptions.warningThreshold) || 99
                                  "
                                />
                                ~
                                <a-input-number
                                  v-model:value="d.analysisOptions.tempOptions.warningThreshold"
                                  :disabled="false"
                                  :min="
                                    Number(d.analysisOptions.tempOptions.criticalThreshold) || 1
                                  "
                                  :max="99"
                                />
                              </a-space>
                            </div>
                            <div class="special-tag-box">
                              <special-tag :type="'success'" :text="t('cm.label.matching')" />
                              <a-space :size="5">
                                <a-input-number
                                  v-model:value="d.analysisOptions.tempOptions.warningThreshold"
                                  disabled
                                />
                                ~
                                <a-input-number value="100" disabled />
                              </a-space>
                            </div>
                          </a-space>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="2">
                          <div class="menu-item-text">{{ $t('cm.title.sensitivity') }}</div>
                          <div style="display: flex; align-items: center">
                            <div style="flex: 1; height: 32px">
                              <a-slider
                                v-model:value="d.analysisOptions.tempOptions.sensitivity"
                                :disabled="false"
                                style="width: 145px"
                                :min="0"
                                :max="1"
                                :step="0.1"
                              />
                            </div>
                            <a-input-number
                              v-model:value="d.analysisOptions.tempOptions.sensitivity"
                              :disabled="false"
                              :min="0"
                              :step="0.1"
                              :max="1"
                              :precision="1"
                            />
                          </div>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="3">
                          <div style="display: flex; margin-top: 5px">
                            <a-button
                              type="primary"
                              ghost
                              :disabled="false"
                              @click="handleResetAnalysisOptions"
                              >{{ $t('common.btn.reset') }}
                            </a-button>
                            <a-space style="margin-left: auto">
                              <a-button
                                :disabled="false"
                                @click="
                                  () => {
                                    d.analysisOptions.closeAnalysisOption(false);
                                    d.showMenu = false;
                                  }
                                "
                              >
                                {{ $t('common.btn.cancel') }}
                              </a-button>
                              <a-button
                                type="primary"
                                :disabled="false"
                                @click="handleApplyAnalysisOptions"
                                >{{ $t('common.btn.apply') }}
                              </a-button>
                            </a-space>
                          </div>
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <a
                      :class="`cm-header-tag-btn-item ${d.showMenu ? 'cm-header-tag-btn-item-special' : ''}`"
                    >
                      <i class="iconfont icon-btn-set" />
                    </a>
                  </a-dropdown>
                </div>
              </template>
            </cm-data-set-table>
          </div>
        </Pane>
        <Pane :size="cmSplitData.paneRightWidth" style="position: relative; overflow: initial">
          <div class="expend-icon-box" :style="{ left: cmSplitData.left }">
            <a-button v-show="cmSplitData.showLeft" class="close-btn" size="small" @click="toLeft">
              <template #icon>
                <left-outlined />
              </template>
            </a-button>
            <a-button v-show="cmSplitData.showRight" class="open-btn" size="small" @click="toRight">
              <template #icon>
                <right-outlined />
              </template>
            </a-button>
          </div>
          <div class="cm-main-box-chart-warp">
            <div v-show="d.showCharts" class="cm-main-box-chart-warp-content">
              <cm-result-chart
                ref="cmResultChart"
                v-model:step-info="stepInfo"
                :allSteps="allSteps"
                :show-charts="d.showCharts"
                :filteredStep="beforeRunStepCheck"
                :box-detail="{
                  commonGroupConfigVO: d.commonGroupConfigVO,
                  useDataNormalization: d.useDataNormalization,
                  traceMatchingType: d.analysisOptions.traceMatchingType,
                  tableData: d.tableData
                }"
                :un-match-step-info="unMatchStepInfo"
                :waferData="meteroloyWaferData"
                :hasMetrology="hasMetrology"
                :tableColumns="d.tableColumns"
                :websocketParams="websocketParams"
                @send-overlay-data="sendOverlayData"
                @changeColumn="changeColumnNameColor"
              />
            </div>
          </div>
        </Pane>
      </splitpanes>
    </div>
    <CmMultiModeTraceView
      ref="chamberMatchingChartModelRef"
      :common-group-config-v-o="d.commonGroupConfigVO"
      :table-data="d.tableData.slice(2)"
      :tree-data="transferTree(d.tableData)"
      :websocket-params="websocketParams"
      :waferData="d.waferData"
      :isHistory="isHistory"
      :tableColumns="d.tableColumns"
      @handle-send-socket="handleOverlaySendSocket"
      @changeColumn="changeColumnNameColor"
    ></CmMultiModeTraceView>
  </div>
  <AddScheduleModel
    ref="addScheduleRef"
    :is-edit="false"
    :alarm-params="{
      groupConfigs: d.commonGroupConfigVO.groupConfigs,
      fileNames: d.commonGroupConfigVO.fileNames!,
      loop: d.commonGroupConfigVO.isLoop,
      runType: d.commonGroupConfigVO.runType,
      stationCompare: d.commonGroupConfigVO.stationCompare
    }"
    @add="addScheduleInfo.addJob"
    @reloadManageList="scheduleManageInfo.reloadManage"
  >
  </AddScheduleModel>
  <cm-schedule-manage-modal
    ref="scheduleManageRef"
    :alarm-params="{
      groupConfigs: d.commonGroupConfigVO.groupConfigs,
      fileNames: d.commonGroupConfigVO.fileNames!,
      loop: d.commonGroupConfigVO.isLoop,
      runType: d.commonGroupConfigVO.runType,
      stationCompare: d.commonGroupConfigVO.stationCompare
    }"
    v-model:canAddSchedule="canAddSchedule"
    @addJob="addScheduleInfo.show"
  />
  <ExportconfigurationModal
    ref="exportconfigurationModalRef"
    :treeData="treeData"
    :clickedRow="clickedRow"
    :hasMetrology="hasMetrology"
    @confirm="handleExportConfigurationConfirm"
  />
  <!-- 新增数据集 -->
  <AddDataset
    ref="datasetModal"
    :showCache="showCache"
    :datasetEntry="datasetEntry"
    :websocketParams="websocketParams"
    :groupingData="d.waferData"
    :isHistory="isHistory"
    v-model:open="showDataset"
    v-model:commonGroupConfig="d.commonGroupConfigVO"
    @showAddDataset="showAddDataset"
    @render-new-group-configs="renderNewGroupConfigs"
    @confirm="confirm"
    @create-new-group-configs="createNewGroupConfigs"
    @changeIsHistory="changeIsHistory"
    @setParameter="setParameter"
    @setRunMode="setRunMode"
  />
  <ReferenceByMetrology
    ref="referenceByMetrologyRef"
    :waferData="meteroloyWaferData"
  ></ReferenceByMetrology>
  <ViewMetrology
    ref="viewMetrologyRef"
    :websocketParams="websocketParams"
    :data="metrologyViewData"
    :noAddDataset="true"
    @metrologyGroup="metrologyGroup"
  />
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');

.cm-main-box {
  height: 100%;
  background-color: @bg-block-color;
  border-radius: @border-circle;
  overflow: hidden;

  // padding: 10px 14px;
  :deep(.btn-disabled) {
    color: @bg-icon-disable-color;
    background: @bg-disabled-color;

    &:hover {
      border-color: @border-color;
    }
  }

  :deep(.chart_icon) {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-all-btn {
    display: flex;
    align-items: center;
    border-bottom: solid @border-color 1px;
    padding: 10px 14px;
    // 处理边框重叠
    margin-bottom: -1px;

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: @text-title-color;
      margin: 0 4px;
    }

    .fullscreen-icon {
      color: @text-weak-text;
      width: 20px;
      height: 20px;
      display: inline-flex;
      justify-content: center;
      align-items: center;

      &:hover {
        cursor: pointer;
        color: @text-weak-text;
        background-color: @bg-hover-2-color;
      }
    }

    &-run-info {
      margin-left: 10px;
      &-span {
        margin-right: 10px;
        padding: 2px 8px;
        font-size: 14px;
        color: @text-subtitle-color;
        background-color: @border-group-color;
        border: 1px solid @border-color;
      }
    }

    &-right {
      margin-left: auto;
    }
  }

  &-table-chart-warp {
    display: flex;
    // padding: 14px;
    height: calc(100% - 53px);

    .cm-header-tag-btn {
      display: inline-flex;
      flex-direction: row;
      align-items: center;

      &-item {
        margin-left: 2px;
        padding: 0 3px;

        &:hover {
          cursor: pointer;
          background-color: @bg-hover-2-color;
        }
      }

      &-item-special {
        color: @primary-color;
      }
    }
    :deep(& > .splitpanes) {
      .splitpanes__pane {
        .expend-icon-box {
          position: absolute;
          top: 10px;
          display: none;
          flex-wrap: nowrap;
          z-index: 10;
          &:hover {
            display: flex;
          }
        }
      }

      & > .splitpanes__splitter:hover ~ .splitpanes__pane {
        .expend-icon-box {
          display: flex;
        }
      }
    }
  }

  &-table-warp {
    width: 100%;
    height: 100%;
  }

  &-chart-warp {
    // margin-left: 9px;
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;

    &-content {
      overflow: hidden;
      width: calc(100% - 14px);
      height: 100%;
    }
  }
}

.ant-dropdown-menu-title-content {
  white-space: wrap !important;
}

.menu-item-text {
  font-weight: bold;
}

.special-tag-box {
  display: flex;

  .base-tag {
    line-height: 30px;
  }
}

.export-loading-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 10px;
  border: 1px solid @border-color;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: not-allowed;
  background-color: @bg-disabled-color;

  .export-loading--spinner {
    display: inline-block;
    position: relative;
    transform: translateY(40%);
    animation: loader 1000ms infinite linear;
    transition: all 1000ms ease;
    z-index: 1;
    border-radius: 50%;
    border: 0.5em solid @primary-color-tag-active;

    font-size: 3px;
    width: 16px;
    height: 16px;
  }

  @keyframes loader {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
      border-left: 0.5em solid @primary-color;
    }

    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
      border-left: 0.5em solid @primary-color;
    }
  }
}
:deep(.splitpanes__pane) {
  overflow: visible !important;
}
</style>
