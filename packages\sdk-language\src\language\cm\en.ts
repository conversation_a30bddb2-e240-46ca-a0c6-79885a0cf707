// 不区分中英文的关键字
import notTranslate from './not-translate';
// 注：不要再在cm、rms下新增common相关的多语言
export default {
  cm: {
    title: {
      chamberMatching: 'Chamber Matching',
      cmMain: 'CM Main',
      parameterCategorization: 'Parameter Categorization',
      cMConfiguration: 'CM Config',
      trimConfig: 'Trimming Config',
      dataTrimmingRule: 'Data Trimming Rule',
      trimOption: 'Trimming Option',
      trimRule: 'Trimming Rule',
      stationParameter: 'Station Parameter',
      datasetMatchRange: 'Dataset Matching Range',
      sensitivity: 'Sensitivity',
      otherOption: 'Other Option',
      runBy: 'Run By',
      fileImport: 'Add Dataset from File',
      dbImport: 'Add Dataset from Database',
      summaryChart: 'Summary Chart',
      traceChart: 'Trace Chart',
      boxplotChart: 'Boxplot Chart',
      scheduleJob: 'Add Schedule Job',
      filter: 'Filter',
      scheduleManage: 'Schedule Job Management',
      jobList: 'Job List',
      eqpDataSources: 'EQP Data Sources',
      modifySchedule: 'Modify Schedule Job',
      datasetConfig: 'Dataset Configuration',
      dataList: 'Data List',
      loopList: 'Loop List',
      csvFileImport: 'File Import',
      fileCount: 'File Count',
      addDataset: 'Add Dataset',
      moveDataset: 'Move Dataset',
      chartConfig: 'Chart Configuration',
      inputMetrologyData: 'Input Metrology Data',
      metrologyChart: 'Metrology Chart',
      reference: 'Reference',
      showLegendByCustom: 'Show Legend By Custom',
      legendBy: 'Legend By',
      select: 'Select',
      total: 'Total'
    },
    label: {
      eqp: 'EQP',
      trimType: 'Data trimming in Recipe Step by',
      startPoint: 'Start Data Points',
      endPoint: 'End Data Points',
      startTime: 'Start Time',
      endTime: 'End Time',
      startPercent: 'Start Percent',
      endPercent: 'End Percent',
      maxTime: 'Max Time',
      maxPercent: 'Max',
      maxCount: 'Max Count',
      topNColOutput: 'Top N columns to output',
      topNParamOutput: 'Top N parameters to output',
      normalization: 'Apply Data Normalization',
      analysisOptions: 'Analysis Options',
      critical: 'Critical',
      warning: 'Warning',
      matching: 'Matching',
      inactive: 'Inactive',
      dataSource: 'Data Source',
      dataSourceDirectoryRoot: 'Directory Root',
      relative: 'Relative',
      specific: 'Specific',
      hours: 'Hours',
      days: 'Days',
      last: 'Last',
      from: 'From',
      to: 'To',
      csvFileCol: 'CSV File Columns',
      datasetListTip:
        'Check the checkbox and click the "+" button to generate a Dataset.You can use the right mouse button to quickly generate a Dataset or delete a row from the Dataset.',
      dataSet: 'DataSet',
      selected: 'Selected',
      selectParameter: 'Select Parameters',
      statType: 'Stat Type',
      jobName: 'Job Name',
      startDate: 'Start Date',
      recurrence: 'Recurrence',
      never: 'Never',
      hourly: 'Hourly',
      daily: 'Daily',
      every: 'Every',
      day: 'Day(s)',
      hour: 'Hour(s)',
      noEndDate: 'No End Date',
      after: 'After',
      occurrences: 'Occurrences',
      endBy: 'End By',
      contextFilter: 'Context Filter',
      regex: 'Regex',
      enableEmailNotification: 'Enable Email Notification',
      notificationRecipients: 'Notification Recipients',
      attachReport: 'Attach Report',
      conditionalNotification: 'Conditional Notification',
      matchThreshold: 'Match Threshold',
      notifyWhen: 'Notify When',
      datasetIsNotMatch: 'dataset is not matching',
      ofDatasetNotMatch: '% of dataset is no matching',
      parameter: 'Parameter',
      autoGroup: 'Automatic grouping',
      matchingRate: 'matching rate',
      matchingParameter: 'matching parameter',
      groupName: 'match parameter group name',
      zeroTicket: 'Auto detect Random Noise parameter and skip analysis',
      filterParameter: 'Filter Parameter',
      filterRecipeStep: 'Filter Recipe Step',
      showUnSelected: 'Show un selected parameter in result'
    },
    field: {
      dataSet: 'DATASET',
      tool: 'TOOL',
      eqp: 'EQP',
      chamber: 'CHAMBER',
      lot: 'LOT',
      wafer: 'WAFER',
      startTime: 'START TIME',
      endTime: 'END TIME',
      min: 'MIN',
      q1: '1Q',
      median: 'MEDIAN',
      q3: '3Q',
      max: 'MAX',
      ruleId: 'RULE ID',
      trimming: 'TRIMMING',
      lotId: 'LOT ID',
      waferId: 'WAFER ID',
      loopNo: 'LOOP NO',
      toolId: 'TOOL ID',
      chamberId: 'CHAMBER ID',
      recipeId: 'RECIPIENT ID',
      recipeStep: 'RECIPE STEP',
      matchingCount: 'MATCHING COUNT',
      matchingPercentage: 'MATCHING %',
      category: 'CATEGORY',
      parameterGroup: 'PARAMETER GROUP',
      parameter: 'PARAMETER',
      chamberName: 'CHAMBER NAME',
      parameterName: 'PARAMETER NAME',
      stationName: 'STATION NAME',
      time: 'TIME',
      value: 'VALUE',
      trigger: 'TRIGGER',
      lastExecutionTime: 'LAST EXECUTION TIME',
      nextRunTime: 'NEXT RUN TIME',
      completedTime: 'COMPLETED TIME',
      status: 'STATUS',
      processDuration: 'PROCESS DURATION',
      reference: 'REFERENCE',
      group: 'GROUP',
      color: 'COLOR',
      display: 'DISPLAY',
      dataType: 'DATA TYPE',
      protocol: 'PROTOCOL',
      ip: 'IP',
      port: 'PORT',
      user: 'USER',
      password: 'PASSWORD',
      remoteDirectory: 'REMOTE DIRECTORY',
      localDirectory: 'LOCAL DIRECTORY',
      schedulerJob: 'SCHEDULE JOB',
      action: 'ACTION',
      customLegend: 'CUSTOM LEGEND',
      recipe: 'RECIPE'
    },
    tips: {
      noGroupTips: 'At least 2 Datasets are required to run analysis.',
      noFile: 'Please select the file to parse',
      fileParseFailure: 'File parsing failure.',
      selectParameters: 'Please Select Parameters.',
      max5parameter: 'Please select parameter maximum 5.',
      onlyOneRecipe: 'In the Station mode, all Recipes within a group must be the same.',
      maxCopyGroup: 'You can copy up to 9 sets at most.',
      noExport: 'There is no data that can be exported.',
      noAddCopyGroup:
        'Do not add any new data to the copied Station. If you need to add new data, please add it to the original Station. The copied Station will automatically synchronize with the new data.',
      matchThreshold:
        'Minimum percentage number in order to consider a dataset to be matched when comparing to the reference Percentage = Matching Parameter / Total Parameters',
      shouldParameters: 'Please select the Parameters first.',
      noParameters: 'No available parameters exist.',
      noFilterTrace: 'Please select at least one piece of data.',
      noCheckSteps: 'No available recipe step exist.',
      oneEqp: 'Only one EQP can be selected',
      onsaveConnotSchedule: 'Cannot create scheduled tasks without saving',
      eqpAndDataType: 'EQP + DATATYPE cannot be duplicated',
      maxGroupCount: 'The maximum number of groups for a DataSet is {count}.',
      enableCustomLegendFirst: 'Please enable "Show Legend By Custom" option in the trace chart page first, then proceed with export',
      groupRepetition: 'Already has group, cannot auto grouping',
      noFdcDb: 'Only in the FDC DB mode can a schedule job be added.',
      changeDataSource:
        'Switching the data source will clear the already grouped dataset. Do you want to continue?',
      changeRunBy:
        'Switching the Run By will clear the already grouped dataset. Do you want to continue?',
      inputParameterName: 'Please input parameter name',
      allFold: 'All Fold',
      allExpand: 'All Expand',
      rename: 'Rename',
      colorOptions: 'Color Options',
      selectCategory: 'Select by Category',
      searchParameterName: 'Search',
      setAsReference: 'Set as Reference',
      cancelReference: 'Cancel Reference',
      noCategory: 'Please select at least one Category.',
      modifySuccess: 'Modified successfully.',
      groupNameExist: 'The name already exists.',
      exportMetrologyTip:
        'Export Metrology Correlation charts when analyzing data with Referenc by Metrology.',
      maxLimitParameters: 'Please select at most {count} parameters',
      clearResult: 'Do you want to clear results?',
      deleteResults: 'Do you want to clear result and delete datasets?',
      generatingChartPreview: 'Generating chart preview...',
      noImageData: 'No image data available',
      selectDataSetFirst: 'Please select a dataset first',
      pValueTip:
        'If it is less than 0.05, the linear regression relationship is significantly valid; otherwise, it is invalid.',
      FStatTip:
        'The larger the value, the stronger the statistical validity of the linear regression relationship.',
      RSquaredTip:
        'Ranges from 0 to 1; the higher the value, the stronger the explanatory power of the line for the data.',
      corrCoeff: 'The slope of the line',
      noMatchingData: 'No matching data found',
      noAvailableData: 'No available data',
      foundMatchingParameters: 'Found {count} matching parameters',
      tooMuchDataSelected: 'Too much data selected, export will take a long time'
    },
    btn: {
      parsedDirectory: 'View Files',
      addFiles: 'Upload',
      overlay: 'Overlay',
      showContexts: 'Show Contexts',
      clearFiles: 'Clear Files',
      addDataset: 'Add Dataset',
      run: 'Run',
      setAnalysisFilter: 'Analysis Filter Setting',
      clearResults: 'Clear Results',
      deleteResults: 'Clear result and delete datasets',
      deleteDataset: 'Delete Dataset',
      clearDataset: 'Clear Dataset',
      customLegend: 'Custom Legend'
    }
  },
  hardwareConfig: {
    label: {
      eqp: 'EQP',
      equipTime: 'Equip Time',
      versionName: 'Version Name',
      versionDetail: 'Version Detail',
      addColOnAfter: 'Add Column on after',
      addColOnBefore: 'Add Column on before',
      deleteCol: 'Delete Column',
      addRowOnAfter: 'Add Row on after',
      addRowOnBefore: 'Add Row on before',
      deleteRow: 'Delete Row',
      workTime: 'Work Time',
      workSummary: 'Work Summary'
    },
    field: {
      equipTime: 'EQUIP TIME',
      versionName: 'VERSION NAME',
      workTime: 'WORK TIME',
      workSummary: 'WORK SUMMARY'
    },
    title: {
      hardwareConfiguration: 'Hardware History'
    },
    tip: {
      copySingle: 'Only one line can be copied at a time.',
      leastOneRow: 'At least one Row.',
      leastOneCol: 'At least one Column.'
    }
  },
  metrology: {
    label: {
      metrologyParameterView: 'Metrology Data View',
      metrologyData: 'Metrology Data',
      measureTime: 'Measure Time',
      lotId: 'Lot ID',
      waferId: 'Wafer ID',
      toolId: 'Tool ID',
      paramName: 'Param Name',
      paramType: 'Param Type',
      sharingScope: 'Scope',
      param: 'Parameter',
      func: 'Func',
      groupCount: 'Group Count',
      groupBy: 'Group By',
      deviceSpecification: 'Device Specification',
      deviceName: 'Device Name',
      diameter: 'Diameter',
      originPosition: 'originPosition',
      notchPosition: 'Notch Position',
      xField: 'X Field',
      yField: 'Y Field',
      siteField: 'Site Field',
      defaultParameter: 'Default Parameter',
      eqp: 'EQP'
    },
    field: {
      lotId: 'LOT ID',
      waferId: 'WAFER ID',
      measureTime: 'MEASURE TIME',
      result: 'RESULT',
      toolId: 'TOOL ID',
      softwareVersion: 'SOFTWARE VERSION',
      recipe: 'RECIPE',
      machineType: 'MACHINE TYPE',
      parameter: 'PARAMETER',
      mean: 'MEAN',
      min: 'MIN',
      max: 'MAX',
      stddev: '%STDDEV',
      sigma: '3 SIGMA',
      range: 'RANGE',
      slotNo: 'SLOT NO',
      paramName: 'PARAM NAME',
      paramType: 'PARAM TYPE',
      sharingScope: 'SCOPE',
      metrology: 'METROLOGY',
      status: 'STATUS',
      process: 'PROCESS',
      link: 'LINK'
    },
    tip: {
      importTip: 'LotId and Measure Time are duplicated. Are you sure you want to overwrite?',
      testVp: 'Please upload a metrology file to test.',
      selectSpecification: 'Please select a Specification.',
      fieldNoInAll: '{field} no in all you upload File.',
      noUpload: 'Please upload a metrology file.',
      noLinked: 'All metrology data need to be Linked',
      noMetrologyData: 'There is no Metrology data.'
    },
    btn: {
      virtualParam: 'Virtual Parameter',
      trendAnalysis: 'Trend Analysis'
    }
  },
  waferMoving: {
    title: {
      analysisDataSet: 'Analysis Data Set'
    },
    tips: {
      maxWafer: 'At most, only 30 wafers can be selected'
    }
  },
  ...notTranslate
};
