import {
  getSchemaMeta,
  cancelRunAnalysisService,
  getConfigByCodeService,
  getContextList
} from '@futurefab/ui-sdk-api';
import moment from 'moment';
import {
  getMetaColumns,
  getAllMetaColumns,
  setWaferLoopsParams
} from '@/views/cm-home/cm-main/add-dataset/config';
import type { MetaResult } from '@/views/cm-home/cm-main/add-dataset/interface';
import cmModelService from '@/services/cm-model.service';
import { cmMainInfo as buttonIds } from '@/log-config';

// 关闭后台run的运行
export const cancelRunAnalysis = async () => {
  await cancelRunAnalysisService({ bodyParams: {} });
};
// 设置history selectRows
const setHistorySelectRows = async (params: any, d: any) => {
  const paramList = await getContextList({
    bodyParams: params,
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-context-list'
    }
  });
  if (paramList.status === 'SUCCESS') {
    d.waferData = paramList.data;
    d.commonGroupConfigVO.groupConfigs.forEach((item: any) => {
      const selectRows = paramList.data.find(
        (row: any) => Number(row.groupId) === Number(item.id)
      )?.selectedRows;
      item.selectedRows = selectRows;
      if (d.commonGroupConfigVO?.isLoop) item.waferLoops = setWaferLoopsParams(selectRows);
    });
  }
};
/**
 * 构建table入参
 */
export const setConfigTable = async (request: any, d: any, renderNewGroupConfigs: any) => {
  const schemaMeta = await getSchemaMeta({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-schema-meta'
    }
  });
  const metaData = schemaMeta.data;
  if (request?.trimRules) d.trimRuleList = request?.trimRules;
  if (metaData) {
    const groupConfigs: any = [];
    const isLoop = request?.loop;
    const contextMap: { key: string; name: any }[] = getMetaColumns(
      metaData,
      ['columnMap'],
      isLoop
    );
    const allContextMap: MetaResult[] = getAllMetaColumns(metaData, ['columnMap']);
    d.commonGroupConfigVO.setContextMap(contextMap);
    d.commonGroupConfigVO.setAllContextMap(allContextMap);
    request?.groupInfos?.forEach((configData: any, i: number) => {
      const { id, name, reference, headerInfo, selectedRows } = configData;
      const groupConfig = d.commonGroupConfigVO.deepCopy();
      groupConfig.sortId = i + 1;
      groupConfig.id = Number(id);
      groupConfig.key = headerInfo.groupNameAlias || name;
      // 分组名称
      groupConfig.name = headerInfo.groupNameAlias || name;
      // 颜色
      groupConfig.color = headerInfo.groupColor;
      groupConfig.chartLegendColor = headerInfo.groupColor;
      groupConfig.reference = reference;
      groupConfig.selectedRows = selectedRows;
      const dynamicSets: { [key: string]: Set<any> } = {};
      contextMap.forEach((item: any) => {
        dynamicSets[item.name] = new Set();
      });
      const allDynamicSets: { [key: string]: Set<any> } = {};
      allContextMap.forEach((item: any) => {
        allDynamicSets[item.name] = new Set();
      });
      for (const key in headerInfo) {
        switch (true) {
          case key === 'From':
            groupConfig.from = moment(headerInfo[key]);
            break;
          case key === 'To':
            groupConfig.to = moment(headerInfo[key]);
            break;
          case key === 'Count':
            groupConfig.count = headerInfo[key];
            break;
          case key === 'Loop Group':
            groupConfig.loopGroup = headerInfo[key];
            break;
          default:
            groupConfig[key] = headerInfo[key];
            break;
        }
      }
      groupConfig.setHeaderInfo(headerInfo);
      groupConfig.setFileNecessaryAttributeConfigs(d.commonGroupConfigVO);
      groupConfigs.push(groupConfig);
    });
    d.commonGroupConfigVO.setLoop(request?.loop);
    d.commonGroupConfigVO.setStationCompare(request?.stationCompare);
    d.commonGroupConfigVO.setRunType('HISTORY');
    // 把这个新对象放进去
    d.commonGroupConfigVO.addGroupConfig(groupConfigs);
    d.showAnalysisSpinner('Preparing data...', false);
    renderNewGroupConfigs(d.commonGroupConfigVO);
    // 修改analysisOptions
    const {
      criticalThreshold,
      warningThreshold,
      similarityThreshold,
      useDataNormalization,
      showNoAnalysisParam,
      detectNonZeroParameter
    } = request;
    d.useDataNormalization = useDataNormalization;
    d.showNoAnalysisParam = showNoAnalysisParam;
    d.zeroTicket = detectNonZeroParameter;
    d.analysisOptions.setAnalysisOptions({
      criticalThreshold,
      warningThreshold,
      similarityThreshold
    });
  }
};
// 构建分析结果
export const setAnalysisResult = (
  result: any,
  d: any,
  websocketParams: any,
  sendWebSocket: any,
  toggleCharts: any,
  request: any
) => {
  d.commonGroupConfigVO.resultGroups = result?.resultMap;
  d.commonGroupConfigVO.analysisOptions = d.analysisOptions;
  const tb = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn();
  d.tableData = tb.tableData;
  d.tableColumns = Object.freeze(tb.tableColumns) as never;
  // 发送websocket
  websocketParams.value = {
    loop: d.commonGroupConfigVO.isLoop,
    historyRequestId: request?.historyRequestId,
    historyRequestDt: request?.historyRequestDt,
    requestDt: result?.requestDt || request?.historyRequestDt,
    requestId: result?.requestId || request?.historyRequestId,
    descriptorValues: d.commonGroupConfigVO.getSelectedDescriptorValues(),
    paramAliasList: [tb.tableData.slice(3, 4)?.[0]?.parameterName],
    groupResultIDs: d.resultIdMap.map((m: any) => [String(m.groupConfigId), m.resultRequestId]),
    recipeStepsForScore: [],
    useSampling: true,
    useDataNormalization: d.useDataNormalization
  };
  sendWebSocket(websocketParams.value);
  // 设置selectRows
  setHistorySelectRows(
    {
      requestDt: result?.requestDt,
      requestId: result?.requestId
    },
    d
  );
  // 展开图表
  toggleCharts(true);
};
/**
 * 页面导出
 */
export const exportPage = (params: any) => {
  cmModelService
    .exportReport(params)
    .then((response) => {
      const element = document.createElement('a');
      const file = 'ChamberMatching_Result_' + moment().format('YYYY-MM-DD HH:mm:ss') + '.html';
      element.setAttribute(
        'href',
        'data:html/plain;charset=utf-8,' + encodeURIComponent(response?.data)
      );
      element.setAttribute('download', file);
      element.style.display = 'none';
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    })
    .catch((reason) => {
      console.log(reason);
    });
};
// 获取限制条件
export const getCmConfigRule = async () => {
  const cmConfigRules = await getConfigByCodeService({
    bodyParams: { code: 'CMConfig' },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-config-by-code'
    }
  });
  // 限制查询多少时间范围内的数据
  const allowedTimeRangeDays = cmConfigRules.data?.allowedTimeRangeDays;
  // 限制最大的分组数
  const maxGroupCount = cmConfigRules.data?.maxGroupCount;
  return {
    allowedTimeRangeDays: {
      name: allowedTimeRangeDays?.name,
      value: allowedTimeRangeDays?.val01,
      unit: allowedTimeRangeDays?.val02
    },
    maxGroupCount: {
      name: maxGroupCount?.name,
      value: maxGroupCount?.val01,
      unit: maxGroupCount?.val02
    }
  };
};
