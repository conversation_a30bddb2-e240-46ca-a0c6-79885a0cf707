<script setup lang="ts">
import { computed, nextTick, reactive, ref, shallowRef, watch, type ShallowRef } from 'vue';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import CmChartCollapse from '@/components/cm-chart-collapse/index.vue';
import CmChartCopy from '@/components/cm-chart-copy/index.vue';
import { useSelfFullScreen } from '@/utils/tools';
import { useGroupLegend } from '../useGroupLegend';
import type { CmFilterObject, LegendTree } from '../interface';
import { Marker } from '@xchart/marker';
import { uniqueId, throttle } from 'lodash-es';
import { getLegendData } from '../components/show-legend/config';
import { useCmChartLegendStore, useBaseStore, useCustomLegendStore } from '@futurefab/ui-sdk-stores';
import ShowLegend from '../components/show-legend/index.vue';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { useMarkerStr } from './useMarkerStr';
import Chart from '@/components/chart.vue';
import ViewDataTable from '../components/view-data-table/index.vue';
import { getColumnFieldList, getEnhanceTemple, getMetrologyOption, getViewDataTableOptions } from './config';
import type { StatisticInfo, StatisticTemplateInfo } from './interface';
import vDraggable from './draggable';
import interact from 'interactjs';
import { CloseOutlined } from '@ant-design/icons-vue';

const chartConfigStore = useCmChartLegendStore();
const customLegendStore = useCustomLegendStore();
const baseStore = useBaseStore();
const props = withDefaults(
  defineProps<{
    waferData: any[];
    paramAlias: string;
    commonGroupConfigVO: any;
    ids: any[];
    metaData: any;
    summaryData: any;
    summaryStep: string;
    summaryType: string;
    contextIndex: any;
    tableColumns?: any[];
    splitLegend?: boolean;
  }>(),
  {
    tableColumns: () => [],
    waferData: () => [],
    paramAlias: '',
    commonGroupConfigVO: () => {},
    ids: () => [],
    metaData: () => {},
    summaryData: () => {},
    contextIndex: () => [],
    splitLegend: true
  }
);
const open = defineModel<boolean>('open', { default: true });
const groupColumns = computed(() =>
  props.tableColumns?.length ? props.tableColumns.slice(1) : []
);
const metrologyRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(metrologyRef);

const statInfo = reactive({
  maxIndex: 10,
  show: true,
  byGroup: false,
  statisticList: [] as Array<StatisticInfo | null>,
  statisticTemplateInfo: [] as StatisticTemplateInfo[],
});
const filterInfo = ref<CmFilterObject>({
  type: 'Group',
  filterKeys: []
});
const chartType = ref<'Trend' | 'Scatter'>('Trend');
const isDeleteBlank = ref(true);
const { customLegendItems, setGroupConfig, initLengend } = useGroupLegend(filterInfo, customLegendStore);
const summaryAndWaferData = ref<any[]>([]);
const mergeWaferDataAndSummaryData = () => {
  const res: any[] = [];
  props.waferData.forEach((item: any) => {
    const groupId = Number(item.groupId);
    const groupName = item.groupName;
    const summaryGroup = props.summaryData.segmentStatData.find((summaryItem: any) => {
      const g = Number(summaryItem.groupConfigId);
      const step = summaryItem.recipestep_name;
      return g === groupId && (step === props.summaryStep || step === props.summaryStep + '_Norm');
    });
    if (summaryGroup) {
      const statIndex = summaryGroup.header.findIndex((stat: string) => stat === props.summaryType);
      const contextIndex = summaryGroup.header.findIndex((key: string) => key === 'context');
      if (statIndex !== -1) {
        const temp = {
          groupId,
          groupName,
          data: item.selectedRows.map((row: any, index: number) => {
            // 找到对应的summary
            const summaryRow = summaryGroup.valueList.find((valueList: string[]) => {
              const contextIndexMap: Record<string, number> = {};
              props.contextIndex.forEach((item: string, index: number) => {
                contextIndexMap[item] = index;
              });
              const context = (valueList[contextIndex] as any)?.split(',');
              const lot = context[contextIndexMap?.lotId];
              const wafer = context[contextIndexMap?.waferId];
              const recipeId = context[contextIndexMap?.recipeId];
              const eqpModuleId = context[contextIndexMap?.eqpModuleId];
              const eqpId = context[contextIndexMap?.eqpId];
              const chamber = context[contextIndexMap?.chamber];
              return (
                row.lotId === lot &&
                row.waferId === wafer &&
                row.recipeId === recipeId &&
                row.eqpId === eqpId &&
                row.chamber === chamber &&
                row.eqpModuleId === eqpModuleId
              );
            });
            return {
              ...row,
              contextKey: summaryRow?.[contextIndex],
              summaryValue: summaryRow?.[statIndex]
            };
          })
        };
        res.push(temp);
      }
    }
  });
  return res;
};

const metrologyOptions = computed(() =>
  Object.keys(props.waferData?.[0].selectedRows?.[0] || {})
    .filter((key) => key.endsWith('-metrology'))
    .map((item) => ({
      label: item.slice(0, -'-metrology'.length),
      value: item
    }))
);
const metrologyParams = ref('');
watch(
  () => metrologyOptions.value,
  () => {
    metrologyParams.value = metrologyOptions.value?.[0].value || '';
  },
  { immediate: true }
);

const viewTableData = computed(() => {
  const temp: any[] = [];
  summaryAndWaferData.value.forEach((item) => {
    const groupId = Number(item.groupId);
    const groupName = item.groupName;
    item.data.forEach((row: any) => {
      temp.push({ ...row, groupName: groupName });
    });
  });
  return temp;
});
const viewTableOptions = computed(() =>
  getViewDataTableOptions(
    props.summaryType,
    metrologyOptions.value.map((item) => item.value)
  )
);
const viewColumnFieldList = computed(() => getColumnFieldList(viewTableOptions.value.columns));
const options = ref({} as any);
const dealOptions = () => {
  loading.value = true;
  try {
    const temp = getMetrologyOption({
      summaryAndWaferData: summaryAndWaferData.value,
      customLegendItems: customLegendItems.value,
      filterInfo: filterInfo.value,
      chartType: chartType.value,
      summaryType: props.summaryType,
      metrologyParams: metrologyParams.value,
      isDeleteBlank: isDeleteBlank.value,
      chartLegendConfig: chartConfigStore.chartLegendConfig,
      groupColumns: groupColumns.value,
      slopeByGroup: statInfo.byGroup,
      customLegendData: customLegendStore.customLegendData,
    });
    options.value = temp.options;
    statInfo.statisticList = temp.statisticInfo;
    statInfo.maxIndex = 10;
    statInfo.statisticTemplateInfo = getEnhanceTemple(temp.statisticInfo);
  } catch {}

  loading.value = false;
};

const filterChart = (info: any) => {
  filterInfo.value = info;
  initLengend();
  dealOptions();
};
const chartContentRef = ref();

const loading = ref(false);
const uid = uniqueId('cm-metrology');
const legendInfo = ref<LegendTree[]>([]);

watch(
  [() => groupColumns.value, () => customLegendStore.customLegendData],
  () => {
    setGroupConfig((groupColumns.value as any) || []);
    filterInfo.value = { type: 'Group', filterKeys: [] };
    legendInfo.value = getLegendData(groupColumns.value, filterInfo, props.metaData, props.ids);
    dealOptions();
  },
  { immediate: true }
);
watch(
  [() => chartConfigStore.chartLegendConfig],
  () => {
    dealOptions();
  },
  { immediate: true }
);
watch(
  [
    () => props.waferData,
    () => props.summaryData,
    () => props.summaryStep,
    () => props.summaryType
  ],
  () => {
    summaryAndWaferData.value = mergeWaferDataAndSummaryData();
    dealOptions();
  },
  { immediate: true }
);

const marker: ShallowRef<Marker | null> = shallowRef(null);
const { chartClick, clearMarker } = useMarkerStr(marker, chartContentRef, uid + '-content');

const viewDataRef = ref();
const viewData = () => {
  viewDataRef.value.show();
};
const changeChartType = () => dealOptions();
const legendChange = () => dealOptions();
const changeParam = () => dealOptions();
const onDeleteBlank = () => {
  isDeleteBlank.value = !isDeleteBlank.value;
  dealOptions();
};
const statMouseDown = (statisticInfo: StatisticTemplateInfo) => {
  statisticInfo.zIndex = ++statInfo.maxIndex;
};
const slopeByGroupChange = () => {
  dealOptions();
};
const title = computed(
  () =>
    `${props.paramAlias} STEP:${props.summaryStep} Metrology:${metrologyParams.value?.slice(0, -'-metrology'.length)} `
);
// 暴露方法给截图页面调用
const switchMetrologyParam = (param: string) => {
  metrologyParams.value = param;
  dealOptions();
};

const getMetrologyOptions = () => metrologyOptions.value;

defineExpose({
  switchMetrologyParam,
  getMetrologyOptions
});

const statisticFormatter = (num?: number) => {
  const fixed = 5;
  const threshold = 6;
  if (num === undefined || num === null || isNaN(num)) {
    return '--';
  }
  // 超大值：科学计数法（保留fixed位小数）
  if (num > threshold) {
    return num.toExponential(fixed);
  }

  return Number(num.toFixed(fixed)).toString();
};
const baseClas = 'cm-metrology-chart';
</script>

<template>
  <div
    ref="metrologyRef"
    :class="baseClas"
    v-isLoading="{
      isShow: loading,
      hasButton: false,
      title: 'Loading...'
    }"
  >
    <CmChartCollapse :is-full-screen="isFullscreen" v-model:open="open">
      <template #header>
        <div :class="baseClas + '-head'">
          <div :class="baseClas + '-head-title'">
            <p>{{ $t('cm.title.metrologyChart') }}</p>
            <vxe-tooltip
              :content="$t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
              :use-h-t-m-l="true"
              theme="light"
              ><i
                class="iconfont chart_icon_icon zoom-icon"
                :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
                style="color: var(--text-hint-color)"
                @click="toggle"
              ></i
            ></vxe-tooltip>
            <a-select
              v-model:value="metrologyParams"
              show-search
              :options="metrologyOptions"
              style="width: 118px"
              :show-arrow="true"
              @change="changeParam"
            >
            </a-select>
          </div>
          <div :class="baseClas + '-head-icons'">
            <a-radio-group
              v-model:value="chartType"
              name="radioGroup"
              :class="baseClas + '-chart-type-radio'"
              @change="changeChartType"
            >
              <a-radio :value="'Trend'">{{ $t('Trend Chart') }}</a-radio>
              <a-radio :value="'Scatter'">{{ $t('Scatter Chart') }}</a-radio>
            </a-radio-group>
            <template v-if="chartType !== 'Trend'">
              <a-tooltip
                v-if="open && chartType === 'Scatter'"
                :placement="'bottomRight'"
                :auto-adjust-overflow="true"
                :overlayClassName="baseClas + '-stat-tooltip ' + (baseStore.language === 'en-US' ? '' : 'zh')"
              >
                <template #title>
                  <p><label> P.Value：</label>{{ $t('cm.tips.pValueTip') }}</p>
                  <p><label> FStat：</label>{{ $t('cm.tips.FStatTip') }}</p>
                  <p><label> RSquared：</label>{{ $t('cm.tips.RSquaredTip') }}</p>
                  <p><label> Corr Coeff：</label>{{ $t('cm.tips.corrCoeff') }}</p>
                </template>
                <a-checkbox v-model:checked="statInfo.show">Stat Info</a-checkbox>
              </a-tooltip>
              <a-switch
                style="margin-right: 10px"
                v-model:checked="statInfo.byGroup"
                checked-children="By Group"
                un-checked-children="By Group"
                @change="slopeByGroupChange"
              />
            </template>
            <show-legend
              ref="legendRef"
              :legend-info="legendInfo"
              :metaData="metaData"
              @filter-chart="filterChart"
            />
            <EesButtonTip
              v-show="chartType === 'Trend'"
              :margin-right="10"
              :is-border="true"
              icon="#icon-btn-blank-delete"
              :text="$t('eesCharts.commonBtn.deleteBlank')"
              :is-active="isDeleteBlank"
              @on-click="onDeleteBlank"
            />
            <ees-button-tip
              :marginRight="0"
              :is-border="true"
              icon="#icon-btn-view-data"
              :text="$t('common.title.viewData')"
              @click="viewData"
            />
          </div>
        </div>
      </template>
      <template #content>
        <div id="cm-metrology-chart-container" style="width: 100%; height: 100%">
          <CmChartCopy>
            <CmGroupLegend
              :legend="customLegendItems"
              :title="title"
              :split-lenged="props.splitLegend"
              @legendChange="legendChange">
              <template #chart>
                <div
                  ref="chartContentRef"
                  :id="uid + '-content'"
                  :class="baseClas + '-chart-content'"
                >
                  <template v-if="statInfo.show">
                    <template v-for="statisticInfo in statInfo.statisticTemplateInfo" :key="statisticInfo">
                        <div
                          v-if="statisticInfo.show"
                          :style="{ left: statisticInfo.originLeft + 'px', zIndex: statisticInfo.zIndex }"
                          :class="baseClas + '-chart-content-stat'"
                          v-draggable="{
                            modifiers: [
                              interact.modifiers.restrictRect({
                                restriction: 'parent', // 限制在父容器内拖动
                                endOnly: true
                              })
                            ]
                          }"
                          @mousedown="statMouseDown(statisticInfo)"
                        >
                          <div v-if="statInfo.byGroup" class="group-name-box">
                            <span :title="statisticInfo.name" class="group-name single-line-ellipsis">{{ statisticInfo.name }}</span>
                            <CloseOutlined class="close-icon" @click="statisticInfo.show = false"/>
                          </div>
                          <div>
                            <label>P.Value</label
                            ><span class="single-line-ellipsis" :title="statisticFormatter(statisticInfo?.pValue)">{{ statisticFormatter(statisticInfo?.pValue) }}</span>
                          </div>
                          <div>
                            <label>FStat</label
                            ><span class="single-line-ellipsis" :title="statisticFormatter(statisticInfo?.fValue)">{{ statisticFormatter(statisticInfo?.fValue) }}</span>
                          </div>
                          <div>
                            <label>RSquared</label
                            ><span class="single-line-ellipsis" :title="statisticFormatter(statisticInfo?.rSquared)">{{ statisticFormatter(statisticInfo?.rSquared) }}</span>
                          </div>
                          <div>
                            <label>Corr Coeff</label
                            ><span class="single-line-ellipsis" :title="statisticFormatter(statisticInfo?.slope)">{{ statisticFormatter(statisticInfo?.slope) }}</span>
                          </div>
                        </div>
                    </template>
                  </template>
                  <Chart
                    v-if="open"
                    id="cm-metrology-chart"
                    :options="options"
                    @chart-click="chartClick($event)"
                    @chart-resize="clearMarker()"
                    @chart-datazoom="clearMarker()"
                    @chart-legendselectchanged="clearMarker()"
                  ></Chart>
                </div>
              </template>
            </CmGroupLegend>
          </CmChartCopy>
        </div>
      </template>
    </CmChartCollapse>
    <Teleport to="body">
      <view-data-table
        ref="viewDataRef"
        :data="viewTableData"
        :options="viewTableOptions"
        :column-field-list="viewColumnFieldList"
      ></view-data-table>
    </Teleport>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-metrology-chart {
  height: 100%;
  width: 100%;
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
  background-color: @bg-color;
  &-head {
    flex-wrap: nowrap;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-title {
      flex-wrap: nowrap;
      display: flex;
      align-items: center;
      p {
        font-size: 16px;
        font-weight: bold;
        line-height: 22px;
        flex-shrink: 0;
        max-width: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin: 0;
        color: @text-title-color;
      }
      .zoom-icon {
        margin: 0 0 0 5px;
        color: @text-title-color;
        width: 20px;
        height: 20px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &:hover {
          cursor: pointer;
          color: @text-weak-text;
          background-color: @bg-hover-2-color;
        }
      }
    }
    &-icons {
      flex-wrap: nowrap;
      display: flex;
      align-items: center;
    }
  }
  &-chart-content {
    height: 100%;
    width: 100%;
    position: relative;
    &-stat {
      max-width: 170px;
      overflow: hidden;
      position: absolute;
      top: -10px;
      left: 120px;
      padding: 10px;
      background-color: @bg-block-color;
      border-radius: 4px;
      border: 1px solid @border-color;
      box-shadow: 0px 2px 8px 0px rgba(2, 12, 29, 0.1);
      display: flex;
      flex-direction: column;
      gap: 2px;
      div {
        display: flex;
        align-items: center;
        label {
          color: @text-sub-color;
          font-size: 12px;
          font-weight: normal;
          line-height: 18px;
          width: 58px;
          flex-shrink: 0;
          margin-right: 10px;
          white-space: nowrap;
        }
        span {
          color: @text-subtitle-color;
        }
        &.group-name-box {
          justify-content: space-between;
          .group-name {
            flex-shrink: 1;
            font-weight: bold;
            color: @text-subtitle-color;
            font-size: 12px;
          }
          .close-icon {
            flex-shrink: 0;
            cursor: pointer;
          }
        }
      }
    }
  }
  &-chart-type-radio {
    :deep(span) {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      color: @text-subtitle-color;
    }
  }
}
</style>
<style lang="less">
.cm-metrology-chart-stat-tooltip {
  width: 800px;
  max-width: 800px;
  .ant-tooltip-content {
    width: 800px;
    .ant-tooltip-inner p {
      display: flex;
      label {
        flex-shrink: 0;
        width: 100px;
        text-align: right;
      }
    }
  }
  &.zh {
    width: 420px;
    max-width: 420px;
    .ant-tooltip-content {
      width: 420px;
    }
  }
}
</style>