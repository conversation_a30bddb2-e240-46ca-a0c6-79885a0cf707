// 不区分中英文的关键字
import notTranslate from './not-translate';
// 注：不要再在cm、rms下新增common相关的多语言
export default {
  cm: {
    title: {
      chamberMatching: '腔室匹配',
      cmMain: 'CM Main',
      parameterCategorization: '参数分类',
      cMConfiguration: 'CM 配置',
      trimConfig: '修剪配置',
      dataTrimmingRule: '数据修剪规则',
      trimOption: '修剪选项',
      trimRule: '修剪规则',
      stationParameter: 'Station 参数',
      datasetMatchRange: 'Dataset Matching Range',
      sensitivity: '灵敏度',
      otherOption: '其他选项',
      runBy: 'Run By',
      fileImport: '从文件中导入数据集',
      dbImport: '从数据库导入数据集',
      summaryChart: 'Summary Chart',
      traceChart: 'Trace Chart',
      boxplotChart: 'Boxplot Chart',
      scheduleJob: 'Add Schedule Job',
      filter: 'Filter',
      scheduleManage: 'Schedule Job Management',
      jobList: 'Job List',
      eqpDataSources: 'EQP Data Sources',
      modifySchedule: 'Modify Schedule Job',
      datasetConfig: '数据集分组',
      dataList: '数据列表',
      loopList: 'Loop List',
      csvFileImport: '文件导入',
      fileCount: '文件数量',
      addDataset: '新增数据集',
      moveDataset: '移动数据集',
      chartConfig: '图表配置',
      inputMetrologyData: '输入量测数据',
      metrologyChart: 'Metrology Chart',
      reference: '参考',
      showLegendByCustom: 'Show Legend By Custom',
      legendBy: 'Legend By',
      select: '选择',
      total: '总计'
    },
    label: {
      eqp: 'EQP',
      trimType: '修剪方式',
      startPoint: 'Start Data Points',
      endPoint: 'End Data Points',
      startTime: 'Start Time',
      endTime: 'End Time',
      startPercent: 'Start Percent',
      endPercent: 'End Percent',
      maxTime: 'Max Time',
      maxPercent: 'Max',
      maxCount: 'Max Count',
      topNColOutput: 'Top N columns to output',
      topNParamOutput: 'Top N parameters to output',
      normalization: '应用数据标准化处理',
      analysisOptions: '分析选项',
      critical: '严重',
      warning: '警告',
      matching: '匹配',
      inactive: '不活跃',
      dataSource: '数据源',
      dataSourceDirectoryRoot: '数据源根目录',
      relative: 'Relative',
      specific: 'Specific',
      hours: '小时',
      days: '天',
      last: 'Last',
      from: 'From',
      to: 'To',
      csvFileCol: 'CSV文件列名',
      datasetListTip:
        '勾选复选框并点击“+”按钮以生成数据集。您可以使用鼠标右键快速生成数据集或从数据集中删除一行。',
      dataSet: 'DataSet',
      selected: 'Selected',
      selectParameter: '选择参数',
      statType: 'Stat Type',
      jobName: '任务名称',
      startDate: '开始时间',
      recurrence: '重现',
      never: '从不',
      hourly: '按小时',
      daily: '按天',
      every: '每',
      day: '天',
      hour: '小时',
      noEndDate: '无结束日期',
      after: 'After',
      occurrences: 'Occurrences',
      endBy: '结束于',
      contextFilter: 'Context 过滤',
      regex: '正则',
      enableEmailNotification: '启用电子邮件通知',
      notificationRecipients: '通知收件人',
      attachReport: '附上报告',
      conditionalNotification: '有条件通知',
      matchThreshold: '匹配阈值',
      notifyWhen: '当',
      datasetIsNotMatch: '数据集不匹配',
      ofDatasetNotMatch: '% 数据集不匹配',
      parameter: '参数',
      autoGroup: '自动分组',
      matchingRate: '匹配率',
      matchingParameter: '匹配参数',
      groupName: '匹配参数分组名称',
      zeroTicket: '自动检测随机噪声参数并跳过分析',
      filterParameter: '参数过滤',
      filterRecipeStep: 'Recipe Step过滤',
      showUnSelected: '在结果中显示未选中的参数'
    },
    field: {
      dataSet: 'DATASET',
      tool: 'TOOL',
      eqp: 'EQP',
      chamber: 'CHAMBER',
      lot: 'LOT',
      wafer: 'WAFER',
      startTime: 'START TIME',
      endTime: 'END TIME',
      min: 'MIN',
      q1: '1Q',
      median: 'MEDIAN',
      q3: '3Q',
      max: 'MAx',
      ruleId: 'RULE ID',
      trimming: 'TRIMMING',
      lotId: 'LOT ID',
      waferId: 'WAFER ID',
      loopNo: 'LOOP NO',
      toolId: 'TOOL ID',
      chamberId: 'CHAMBER ID',
      recipeId: 'RECIPIENT ID',
      recipeStep: 'RECIPE STEP',
      matchingCount: 'MATCHING COUNT',
      matchingPercentage: 'MATCHING %',
      category: 'CATEGORY',
      parameterGroup: 'PARAMETER GROUP',
      parameter: 'PARAMETER',
      chamberName: 'CHAMBER NAME',
      parameterName: 'PARAMETER NAME',
      stationName: 'STATION NAME',
      time: 'TIME',
      value: 'VALUE',
      trigger: 'TRIGGER',
      lastExecutionTime: 'LAST EXECUTION TIME',
      nextRunTime: 'NEXT RUN TIME',
      completedTime: 'COMPLETED TIME',
      status: 'STATUS',
      processDuration: 'PROCESS DURATION',
      reference: 'REFERENCE',
      group: 'GROUP',
      color: 'COLOR',
      display: 'DISPLAY',
      dataType: 'DATA TYPE',
      protocol: 'PROTOCOL',
      ip: 'IP',
      port: 'PORT',
      user: 'USER',
      password: 'PASSWORD',
      remoteDirectory: 'REMOTE DIRECTORY',
      localDirectory: 'LOCAL DIRECTORY',
      schedulerJob: 'SCHEDULE JOB',
      action: 'ACTION',
      customLegend: 'CUSTOM LEGEND',
      recipe: 'RECIPE'
    },
    tips: {
      noGroupTips: '至少需要2个数据集来运行分析',
      noFile: '请选择要解析的文件',
      fileParseFailure: '文件解析失败',
      selectParameters: '请选择参数',
      max5parameter: '最多选5个参数',
      onlyOneRecipe: 'Station模式下，所有分组内的Recipe都必须相同',
      maxCopyGroup: '最多复制9组',
      noExport: '没有数据可以导出',
      noAddCopyGroup:
        '禁止给复制出来的Station新增数据，如需新增数据请给原始Station新增，复制出来的Station会自动同步新增的数据',
      matchThreshold:
        '在将参考数据集与之进行比较时，为认为数据集匹配所需的最小百分比数值（百分比=匹配参数/总参数）',
      shouldParameters: '请先勾选Parameters',
      noParameters: '请至少勾选一个参数',
      noFilterTrace: '请最少选择一条数据',
      noCheckSteps: '请至少勾选一个recipe step',
      oneEqp: '只能选一个EQP',
      onsaveConnotSchedule: '没有保存不能创建定时任务',
      eqpAndDataType: 'EQP + DATATYPE 不能重复',
      maxGroupCount: 'DataSet分组最多为{count}组',
      enableCustomLegendFirst: '请先在Trace Chart页面开启"Show Legend By Custom"选项，然后再进行导出操作',
      groupRepetition: '已分组，无法自动分组',
      noFdcDb: '只有在FDC DB模式下可以新增Schedule Job',
      changeDataSource: '切换Datasource的话，会清空已经分组的dataset，继续吗？',
      changeRunBy: '切换Run By的话，会清空已经分组的dataset，继续吗？',
      selectCategory: '请选择分类',
      inputParameterName: '请输入参数名称',
      allFold: '全部折叠',
      allExpand: '全部展开',
      rename: '重命名',
      colorOptions: '颜色选项',
      searchParameterName: '请输入参数名称',
      setAsReference: '设置为参考数据集',
      cancelReference: '取消参考数据集',
      noCategory: '请至少勾选一个分类',
      modifySuccess: '修改成功',
      groupNameExist: '名称已存在',
      exportMetrologyTip:
        '当使用Referenc by Metrology分析数据时，可导出Metrology Correlation chart',
      maxLimitParameters: '最多选{count}个参数',
      clearResult: '您想要清除结果吗？',
      deleteResults: '您想要清除结果并删除数据集吗？',
      generatingChartPreview: '正在生成图表预览...',
      noImageData: '暂无图片数据',
      selectDataSetFirst: '请先选择数据集才能选择参数',
      pValueTip: '若小于0.05，直线显著有效；反之无效',
      FStatTip: '值越大，直线的统计学有效性越强',
      RSquaredTip: '0 ~ 1，值越高，直线对数据的解释力越强',
      corrCoeff: '直线的斜率',
      noMatchingData: '没有找到匹配的数据',
      noAvailableData: '没有可用数据',
      foundMatchingParameters: '找到 {count} 个匹配的参数',
      tooMuchDataSelected: '选择数据过多，导出时间会耗时比较长'
    },
    btn: {
      parsedDirectory: '获取待解析文件目录',
      addFiles: '上传文件',
      overlay: 'Overlay',
      showContexts: '图表数据筛选',
      clearFiles: '清除文件',
      addDataset: '新增数据集',
      run: '运行',
      setAnalysisFilter: '分析筛选设置',
      clearResults: '清空结果',
      deleteResults: '清空结果并删除数据集',
      deleteDataset: '删除数据集',
      clearDataset: '清空数据集',
      customLegend: '自定义图例'
    }
  },
  hardwareConfig: {
    label: {
      eqp: 'EQP',
      equipTime: 'Equip Time',
      versionName: 'Version Name',
      versionDetail: 'Version Detail',
      addColOnAfter: 'Add Col on after',
      addColOnBefore: 'Add Col on before',
      deleteCol: 'Delete Col',
      addRowOnAfter: 'Add Row on after',
      addRowOnBefore: 'Add Row on before',
      deleteRow: 'Delete Row',
      workTime: 'Work Time',
      workSummary: 'Work Summary'
    },
    title: {
      hardwareConfiguration: 'Hardware History'
    },
    field: {
      equipTime: 'EQUIP TIME',
      versionName: 'VERSION NAME',
      workTime: 'WORK TIME',
      workSummary: 'WORK SUMMARY'
    },
    tip: {
      copySingle: '一次只能复制一行',
      leastOneRow: '至少要有一行',
      leastOneCol: '至少要有一列'
    }
  },
  metrology: {
    label: {
      metrologyParameterView: '量测数据',
      metrologyData: '量测数据',
      measureTime: '量测时间',
      lotId: 'Lot ID',
      waferId: 'Wafer ID',
      toolId: 'Tool ID',
      paramName: 'Param Name',
      paramType: 'Param Type',
      sharingScope: 'Scope',
      param: 'Parameter',
      func: 'Func',
      groupCount: 'Group Count',
      groupBy: 'Group By',
      deviceSpecification: 'Device Specification',
      deviceName: 'Device Name',
      diameter: 'Diameter',
      originPosition: 'originPosition',
      notchPosition: 'Notch Position',
      xField: 'X Field',
      yField: 'Y Field',
      siteField: 'Site Field',
      defaultParameter: 'Default Parameter',
      eqp: 'EQP'
    },
    field: {
      lotId: 'LOT ID',
      waferId: 'WAFER ID',
      measureTime: 'MEASURE TIME',
      result: 'RESULT',
      toolId: 'TOOL ID',
      softwareVersion: 'SOFTWARE VERSION',
      recipe: 'RECIPE',
      machineType: 'MACHINE TYPE',
      parameter: 'PARAMETER',
      mean: 'MEAN',
      min: 'MIN',
      max: 'MAX',
      stddev: '%STDDEV',
      sigma: '3 SIGMA',
      range: 'RANGE',
      slotNo: 'SLOT NO',
      paramName: 'PARAM NAME',
      paramType: 'PARAM TYPE',
      sharingScope: 'SCOPE',
      metrology: 'METROLOGY',
      status: 'STATUS',
      process: 'PROCESS',
      link: 'LINK'
    },
    tip: {
      importTip: 'lotId, Measure Time重复，确认覆盖吗？',
      testVp: '请上传一个量测数据进行测试',
      selectSpecification: '请选择一个规格',
      fieldNoInAll: '不是所有文件中都含有{field}字段',
      noUpload: '请上传一个量测数据文件',
      noLinked: '所有行都要Link数据',
      noMetrologyData: '没有量测数据'
    },
    btn: {
      virtualParam: '虚拟参数',
      trendAnalysis: '趋势分析'
    }
  },
  waferMoving: {
    title: {
      analysisDataSet: 'Analysis Data Set'
    },
    tips: {
      maxWafer: '最多选择30个Wafer'
    }
  },
  ...notTranslate
};
