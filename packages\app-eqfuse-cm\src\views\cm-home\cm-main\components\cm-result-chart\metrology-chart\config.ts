import { VXETable } from '@futurefab/vxe-table';
import type { CmFilterObject } from '../interface';
import { useCmChartLegendStore, useBaseStore } from '@futurefab/ui-sdk-stores';
import {
  cmChartContextFilter,
  cmChartLegendFilter,
  formatterYSM,
  getChartAxisRange
} from '@/utils';
import dayjs from 'dayjs';
import { CM_CHART_DEFUALT_FONT_SIZE, CM_CHART_DEFUALT_Y_MAX_TICK } from '@/constant/charts';
import { ftest, ttest } from 'jstat';
import type { MetrologyOption, StatisticInfo, StatisticTemplateInfo } from './interface';

const baseStore = useBaseStore();
export const getColumnFieldList = (columns: any[]) => {
  return columns.filter((item) => item.field).map((item) => item.field);
};

export const getViewDataTableOptions = (summaryType: string, metrologyCols: string[]) => {
  return VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      tableName: 'common.title.viewData',
      import: false,
      export: true,
      refresh: false
    },
    columns: [
      {
        field: 'groupName',
        title: 'cm.field.dataSet',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      ...metrologyCols.map((item: any) => ({
        field: item,
        title: item.slice(0, -'-metrology'.length),
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {},
        className: 'add-metrology-cell'
      })),
      {
        field: 'summaryValue',
        title: summaryType || 'SUMMARY',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'eqpId',
        title: 'cm.field.eqp',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'chamber',
        title: 'cm.field.chamber',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'lotId',
        title: 'cm.field.lot',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'wafer',
        title: 'cm.field.wafer',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'StartTime',
        title: 'cm.field.startTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'EndTime',
        title: 'cm.field.endTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      }
    ]
  });
};

const getLineColor = () => {
  return baseStore.theme === 'light' ? '#ECEEF0' : '#556070';
};
const getLabelColor = () => {
  return baseStore.theme === 'light' ? '#4e5969' : '#e2e4e7';
};
const yAxis = (chartLegendConfig: any) => ({
  axisLabel: {
    color: getLabelColor(),
    hideOverlap: true,
    formatter: (v: any) => formatterYSM(v, 4),
    fontSize: chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE
  },
  axisLine: {
    show: true,
    lineStyle: { width: 2, color: getLineColor() }
  },
  splitLine: {
    show: false
  },
  axisTick: {
    show: true,
    lineStyle: { width: 2, color: getLineColor() }
  },
  scale: true,
  splitNumber: (chartLegendConfig?.yAxisMaxTicksLimit || CM_CHART_DEFUALT_Y_MAX_TICK) - 3,
  min: ({ min, max }: any) => {
    return getChartAxisRange(min, max, 0.1)[0];
  },
  max: ({ min, max }: any) => {
    return getChartAxisRange(min, max, 0.1)[1];
  },
  nameTextStyle: {
    color: getLabelColor(),
    fontSize: chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE
  }
});
const dealSlope = (series: any[], xRange: number[], yRange: number[], lineInfos: ({k: number; b: number} | null)[], byGroup: boolean) => {
  const markLineOption = {
    symbolSize: 0,
    lineStyle: { type: 'solid', color: byGroup ? undefined : '#49c6f2' },
    label: { show: false },
    data: [] as any
  };
  lineInfos.forEach((lineInfo, index) => {
    let markData = [];
    if (lineInfo) {
      if (lineInfo.k === Infinity) {
        // 垂直于 x 轴
        markData = [{ xAxis: series[0].data[0].value[0] }];
      } else {
        // y = kx + b 其中 x 范围 要在 xRange
        if (lineInfo.k === 0) {
          markData = [{ xAxis: series[0].data[0].value[1] }];
        } else {
          const pointes = [];
          // 计算直线与四边形(值域 yRange 和定义域 xRange )的交点, 肯定有两个交点
          const y1 = lineInfo.k * xRange[0] + lineInfo.b!; // 带入 x边界 求 y, 看 y 是否在值域内
          if (y1 >= yRange[0] && y1 <= yRange[1]) {
            // 找到一个交点 [xRange[0]， y1]; 左边界
            pointes.push([xRange[0], y1]);
          }
          const y2 = lineInfo.k * xRange[1] + lineInfo.b!;
          if (y2 >= yRange[0] && y2 <= yRange[1]) {
            // 找到一个交点 [xRange[1], y2]; 右边界
            pointes.push([xRange[1], y2]);
          }
          if (pointes.length < 2) {
            const x1 = (yRange[0] - lineInfo.b!) / lineInfo.k;
            if (x1 >= xRange[0] && x1 <= xRange[1]) {
              // 找到一个交点 [x1, yRange[0]]; 下边界
              pointes.push([x1, yRange[0]]);
            }
          }
          if (pointes.length < 2) {
            const x2 = (yRange[1] - lineInfo.b!) / lineInfo.k;
            if (x2 >= xRange[0] && x2 <= xRange[1]) {
              // 找到一个交点 [xRange[2], y2]; 上边界
              pointes.push([x2, yRange[1]]);
            }
          }
          markData = [
            [
              {
                name: 'slope',
                coord: pointes[0]
              },
              {
                coord: pointes[1]
              }
            ]
          ];
        }
      }
      series[index].markLine = {...markLineOption, data: markData};
    }
  });
};
export const getEnhanceTemple = (statisticInfos: Array<StatisticInfo | null>): StatisticTemplateInfo[] => {
  const originZIndex = 10;
  const left = 120;
  const itemWidth = 180;
  const filterStat: StatisticInfo[]  = statisticInfos.filter(item => item !== null);
  return filterStat.map((item: StatisticInfo, index) => ({...item, show: true, zIndex: originZIndex, originLeft: left + itemWidth * index }));
}
export const getMetrologyOption = ({
  summaryAndWaferData,
  customLegendItems,
  filterInfo,
  chartType,
  summaryType,
  metrologyParams,
  isDeleteBlank,
  chartLegendConfig,
  groupColumns,
  slopeByGroup,
  customLegendData,
}: MetrologyOption) => {
  const series: any = [];
  const yRange = [Infinity, -Infinity]; // param 做 Y 轴
  const xRange = [Infinity, -Infinity]; // summary做 x 轴
  const allXaixsValue = new Set<number>();
  let xAxisData: any = [];
  let statisticInfo: Array<StatisticInfo | null> = [];

  // 先过滤数据
  const filterData: { groupId: number; groupName: string; filterData: any[]; }[] = [];
  summaryAndWaferData.forEach((item: any) => {
    const groupId = Number(item.groupId);
    const groupName = item.groupName;
    const temp = item.data.filter((dataItem: any) => {
      const eqpId = dataItem.eqpId ? dataItem.eqpId : dataItem.eqpModuleId?.split(':')[1];
      return (
        cmChartContextFilter(filterInfo as CmFilterObject, {
          group: groupId,
          eqpId,
          chamber: dataItem.chamber,
          recipeId: dataItem.recipeId
        }) && cmChartLegendFilter(customLegendItems, groupId, customLegendData, dataItem.contextKey)
      );
    });
    if (temp.length) {
      filterData.push({ groupId, groupName, filterData: temp });
    }
  });

  const tempMap: Record<string, {
    id: string | number;
    name: string;
    color: string;
    filterData: any[];
  }> = {};
  
  if (!customLegendData.legendByCustom) {
    // 按 groupId 组织数据
    filterData.forEach((item) => {
      const groupId = item.groupId;
      const legendConfig = groupColumns.find((customLegend) => Number(customLegend.id) === groupId);
      tempMap[groupId] = {
        id: item.groupId,
        name: item.groupName,
        filterData: item.filterData,
        color: legendConfig?.color,
      };
    })
  } else {
    // 按照 customLegend + color 组织数据
    filterData.forEach((item) => {
      const groupId = item.groupId;
      item.filterData.forEach((dataItem) => {
        const row = customLegendData.lineItems
          .find(item => item.contextKey === dataItem.contextKey && Number(item.dataSet) === groupId);
        const color = row!.color;
        const customLegend = row!.customLegendName;
        const legendColor = customLegend + '@_@' + color;
        if (!tempMap[legendColor]) {
          tempMap[legendColor] = {
            id: legendColor,
            color,
            name: customLegend,
            filterData: [dataItem]
          };
        } else {
          tempMap[legendColor].filterData.push(dataItem);
        }
      })
    })
  }

  Object.values(tempMap).forEach(item => {
    if (chartType === 'Trend') {
      const summarySer: any = {
        type: 'scatter',
        yAxisIndex: 0,
        data: [],
        symbolSize: 8,
        color: item.color,
        itemStyle: { opacity: 1 }
      };
      const paramSer: any = {
        type: 'scatter',
        symbol: 'triangle',
        yAxisIndex: 1,
        symbolSize: 10,
        data: [],
        color: item?.color,
        itemStyle: { opacity: 1 }
      };
      item.filterData.forEach((rowItem: any) => {
        const time = new Date(rowItem.StartTime).getTime();
        summarySer.data.push({
          info: {
            ...rowItem,
            summaryType: summaryType,
            id: item.id,
            name: item.name,
            seriesDataType: 'summary'
          },
          value: [time, rowItem.summaryValue]
        });
        paramSer.data.push({
          info: {
            ...rowItem,
            metrologyField: metrologyParams,
            id: item.id,
            name: item.name,
            seriesDataType: 'metrology'
          },
          value: [time, rowItem[metrologyParams]]
        });
        if (isDeleteBlank) {
          allXaixsValue.add(time);
        }
      });
      series.push(summarySer, paramSer);
    } else {
      const seriesItem: any = {
        type: 'scatter',
        yAxisIndex: 0,
        name: item.name,
        data: [],
        symbolSize: 10,
        color: item?.color,
        itemStyle: { opacity: 1 },
        zlevel: 1
      };
      item.filterData.forEach((rowItem: any) => {
        const summary = Number(rowItem.summaryValue);
        const metrology = Number(rowItem[metrologyParams]);
        if (isNaN(summary) || isNaN(metrology)) {
          // 过滤异常数据
          return;
        }
        seriesItem.data.push({
          info: {
            ...rowItem,
            summaryType,
            id: item.id,
            name: item.name,
            seriesDataType: 'summary'
          },
          value: [summary, metrology]
        });
        if (isDeleteBlank) {
          allXaixsValue.add(summary);
        }
        // 统计 param 和 summary的范围， 用来计算 slope
        yRange[0] = Math.min(yRange[0], rowItem[metrologyParams]);
        yRange[1] = Math.max(yRange[1], rowItem[metrologyParams]);
        xRange[0] = Math.min(xRange[0], rowItem.summaryValue);
        xRange[1] = Math.max(xRange[1], rowItem.summaryValue);
      });
      series.push(seriesItem);
    }
  });
  if (chartType === 'Scatter') {
    if (slopeByGroup) {
      statisticInfo = series.map((seriesItem: any) => {
        const seriesData = seriesItem.data.map((dataItem: any) => dataItem.value);
        return getMetrologStatistic(seriesData, seriesItem.name);
      });
    } else {
      const allData = series
        .map((seriesItem: any) => seriesItem.data.map((dataItem: any) => dataItem.value))
        .flat();
    
      statisticInfo = [getMetrologStatistic(allData)];
    }
    // 计算 slope
    dealSlope(series, xRange, yRange, statisticInfo.map(item => (item ? {k: item.slope, b: item.intercept} : null)), slopeByGroup);
  }

  if (isDeleteBlank && chartType === 'Trend') {
    const temp = [...allXaixsValue].sort((a, b) => a - b);
    const valueToIndex: Record<number, number> = {};
    temp.forEach((item, index) => {
      valueToIndex[item] = index;
    });
    // 將 x 从值 改为对应的索引
    series.forEach((ser: any) => {
      ser.data.forEach((dataItem: any) => {
        dataItem.value[0] = valueToIndex[dataItem.value[0]];
      });
    });
    xAxisData = temp.map((item) => dayjs(item).format('YYYY/MM/DD\nHH:mm'));
  }

  const res = {
    animation: false,
    grid: {
      left: 20,
      right: chartType === 'Trend' ? 30 : 75,
      top: 30,
      bottom: 0,
      containLabel: true
    },
    legend: { show: false },
    xAxis: {
      type: chartType === 'Trend' ? (isDeleteBlank ? 'category' : 'time') : 'value',
      axisLabel: {
        color: getLabelColor(),
        formatter: isDeleteBlank
          ? undefined
          : chartType === 'Trend'
            ? '{yyyy}/{MM}/{dd}\n{HH}:{mm}'
            : (v: any) => formatterYSM(v, 4),
        hideOverlap: true,
        fontFamily: 'Nunito Sans',
        fontSize: chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE
      },
      nameTextStyle: {
        color: getLabelColor()
      },
      nameGap: 5,
      name: chartType === 'Trend' ? '' : summaryType,
      data: isDeleteBlank ? xAxisData : undefined,
      position: 'bottom',
      boundaryGap: ['3%', '3%'],
      axisLine: {
        onZero: false,
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      },
      axisTick: {
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      },
      splitLine: {
        show: false
      },
      min: ({ min, max }: any) => {
        return getChartAxisRange(min, max, 0.02)[0];
      },
      max: ({ min, max }: any) => {
        return getChartAxisRange(min, max, 0.02)[1];
      }
    },
    toolbox: {
      show: true,
      showTitle: false,
      feature: {
        dataZoom: { enableBrushBack: true },
        brush: { type: ['rect'] }
      },
      iconStyle: { opacity: 0 }
    },
    yAxis:
      chartType === 'Trend'
        ? [
            { ...yAxis(chartLegendConfig), position: 'left', name: summaryType },
            { ...yAxis(chartLegendConfig), position: 'right', name: metrologyParams?.slice(0, -'-metrology'.length) }
          ]
        : { ...yAxis(chartLegendConfig), position: 'left', name: metrologyParams?.slice(0, -'-metrology'.length) },
    series
  };

  return { options: res, statisticInfo };
};
// 计算线性回归统计指标
export const getMetrologStatistic = (points: Array<[number, number]>, name?: string) => {
  const n = points.length;
  if (n < 2) {
    return null;
  }

  // 计算均值和相关总和
  let sumX = 0,
    sumY = 0,
    sumXY = 0,
    sumXX = 0,
    sumYY = 0;
  for (const point of points) {
    sumX += point[0];
    sumY += point[1];
    sumXY += point[0] * point[1];
    sumXX += point[0] * point[0];
    sumYY += point[1] * point[1];
  }

  const meanX = sumX / n;
  const meanY = sumY / n;

  // 计算x的离差平方和 (SSxx)
  const ssxx = sumXX - (sumX * sumX) / n;
  
  // 计算y的离差平方和 (SSyy)
  const ssyy = sumYY - (sumY * sumY) / n;

  // 处理所有x值相同的情况（斜率不存在）
  if (Math.abs(ssxx) < Number.EPSILON) {
    // 所有x值相同，回归直线是垂直的，使用均值作为最佳估计
    const intercept = meanY; // 此时y的最佳估计是其均值
    
    // 所有点的预测值都是y的均值
    const sse = ssyy; // 残差平方和等于总平方和
    const ssr = 0;    // 回归平方和为0
    const sst = ssyy;
    
    return {
      slope: Infinity,  // 表示斜率不存在（垂直直线）
      intercept,
      rSquared: 0,      // 无法通过x预测y
      fValue: 0,
      pValue: 1,        // 不显著
      pSlope: 1,         // 不显著
      name
    };
  }

  // 计算斜率和截距
  const slope = (sumXY - (sumX * sumY) / n) / ssxx;
  const intercept = meanY - slope * meanX;

  // 处理斜率为0的情况（水平线）
  const isSlopeZero = Math.abs(slope) < Number.EPSILON;

  // 计算预测值、残差和平方和
  let ssr = 0; // 回归平方和
  let sse = 0; // 残差平方和
  let sst = 0; // 总平方和

  for (const point of points) {
    const yPred = slope * point[0] + intercept;
    ssr += Math.pow(yPred - meanY, 2);
    sse += Math.pow(point[1] - yPred, 2);
    sst += Math.pow(point[1] - meanY, 2);
  }

  // 处理总平方和为0的情况（所有y值相同）
  if (Math.abs(sst) < Number.EPSILON) {
    return {
      slope: 0,          // 水平线
      intercept: meanY,  // y值都相同
      rSquared: 1,       // 完美拟合
      fValue: Infinity,  // 无穷大
      pValue: 0,         // 完全显著
      pSlope: 0,          // 完全显著
      name
    };
  }

  // 计算R平方
  const rSquared = ssr / sst;

  // 计算F值和P值
  let fValue = 0;
  let pValue = 1;
  if (!isSlopeZero && sse > 0) {
    fValue = ssr / 1 / (sse / (n - 2));
    pValue = 1 - ftest(fValue, 1, n - 2);
  }

  // 计算斜率的标准误、t统计量和P值
  let seSlope = 0;
  let tStatSlope = 0;
  let pSlope = 1;
  
  if (!isSlopeZero) {
    seSlope = Math.sqrt(sse / (n - 2)) / Math.sqrt(ssxx);
    tStatSlope = slope / seSlope;
    pSlope = 2 * (1 - ttest(Math.abs(tStatSlope), n - 2));
  }

  return {
    slope: isSlopeZero ? 0 : slope, // 明确返回0而不是极小值
    intercept,
    rSquared,
    fValue,
    pValue,
    pSlope,
    name
  };
};
