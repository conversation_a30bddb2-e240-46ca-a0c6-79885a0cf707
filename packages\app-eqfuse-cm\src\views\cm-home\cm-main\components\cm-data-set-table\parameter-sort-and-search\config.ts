import { cloneDeep } from 'lodash-es';
import { ResultGroupInfo } from '@/model/ResultGroupInfo';

export interface GroupConfig {
  id: any;
  reference?: boolean;
  resultGroupInfo?: ResultGroupInfo;
  resultGroupInfos?: ResultGroupInfo[];
}
export interface MatchingInfo {
  unmatchingCountAll: number;
  warningCountAll: number;
  matchingCountAll: number;
  missingCountAll: number;
  totalCountAll: number;
}

export const filterCategoryAndParameter = (
  columnData: any,
  originData: any,
  categoryCodeIds: string[],
  searchContent: string
) => {
  // 计算第二行
  const tempOneTwoLines = originData.slice(0, 2);
  const newColumns = computedRow2DataForSearch(
    columnData,
    tempOneTwoLines,
    categoryCodeIds,
    searchContent
  );
  // 计算第三行及以后的数据
  let newTableData: any = originData.slice(0, 2);
  originData.slice(2).forEach((deepOne: any) => {
    if (deepOne?.groupId === null && deepOne?.groupConfigId) {
      // 分组行，直接添加
      newTableData.push(deepOne);
    } else {
      // 参数行，需要根据categoryCodeId和searchContent进行过滤
      const shouldInclude = filterParameterByCategory(deepOne, categoryCodeIds, searchContent);
      if (shouldInclude) newTableData.push(deepOne);
    }
  });
  // 如果两个分组内容在前后,就证明其后面没有参数内容,就剔除掉这两个分组,若分组在最后一行也证明其后面没有参数内容了,也需要剔除
  newTableData = newTableData.filter((item: any, index: number) => {
    if (item?.groupId === null && item?.groupConfigId) {
      if (newTableData[index + 1]?.groupId === null && newTableData[index + 1]?.groupConfigId) {
        return false;
      }
      if (index === newTableData.length - 1) {
        return false;
      }
    }
    return true;
  });
  return { newTableData, newColumns };
};
// 检查参数是否应该包含在结果中
const filterParameterByCategory = (
  parameter: any,
  categoryCodeIds: string[],
  searchContent: string
) => {
  const categoryMatch =
    categoryCodeIds.length === 0 || categoryCodeIds.includes(parameter.categoryCodeId);
  const nameMatch =
    !searchContent || parameter.parameterName?.toLowerCase().includes(searchContent.toLowerCase());
  return categoryMatch && nameMatch;
};
// 计算分组后的match分数值
export const getMatchingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let matchingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      matchingCount += item.matchingCount;
    });
  return totalCount < 1 ? '0' : ((matchingCount / totalCount) * 100).toFixed(2);
};
export const getUnmatchingWarningRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let unmatchingCount = 0;
  let warningCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      unmatchingCount += item.unmatchingCount;
      warningCount += item.warningCount;
    });
  return totalCount < 1 ? '0' : ((unmatchingCount + warningCount / totalCount) * 100).toFixed(2);
};
export const getUnmatchingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let unmatchingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      unmatchingCount += item.unmatchingCount;
    });
  return totalCount < 1 ? '0' : ((unmatchingCount / totalCount) * 100).toFixed(2);
};
export const getWarningRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let warningCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      warningCount += item.warningCount;
    });
  return totalCount < 1 ? '0' : ((warningCount / totalCount) * 100).toFixed(2);
};
export const getMissingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let missingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      missingCount += item.missingCount;
    });
  return totalCount < 1 ? '0' : ((missingCount / totalCount) * 100).toFixed(2);
};
// 列排序规则
const sortResultInfo = (resultGroupInfo: ResultGroupInfo[]) => {
  return resultGroupInfo.sort((a: any, b: any) => {
    // 标题排在最前面
    if (Array.isArray(a) || Array.isArray(b)) return Array.isArray(a) ? -1 : 1;
    // 首先按 reference 排序，为 true 的排在前面
    if (a.reference !== b.reference) return a.reference ? -1 : 1;
    // 按 matchingRatio 升序排列
    const aMatchingRatio = Number(getMatchingRatio(a?.resultGroupInfos, a?.id)) ?? Infinity;
    const bMatchingRatio = Number(getMatchingRatio(b?.resultGroupInfos, b?.id)) ?? Infinity;
    if (aMatchingRatio !== bMatchingRatio) return aMatchingRatio - bMatchingRatio;

    // 按 getUnmatchingWarningRatio 降序排列
    const aUnmatchingWarningRatio =
      Number(getUnmatchingWarningRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bUnmatchingWarningRatio =
      Number(getUnmatchingWarningRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aUnmatchingWarningRatio !== bUnmatchingWarningRatio) {
      return bUnmatchingWarningRatio - aUnmatchingWarningRatio;
    }
    // 按 getUnmatchingRatio 降序排列
    const aUnmatchingRatio = Number(getUnmatchingRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bUnmatchingRatio = Number(getUnmatchingRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aUnmatchingRatio !== bUnmatchingRatio) {
      return bUnmatchingRatio - aUnmatchingRatio;
    }
    // 按 getWarningRatio 降序排列
    const aWarningRatio = Number(getWarningRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bWarningRatio = Number(getWarningRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aWarningRatio !== bWarningRatio) {
      return bWarningRatio - aWarningRatio;
    }
    // 按 getMissingRatio 升序排列
    const aMissingRatio = Number(getMissingRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bMissingRatio = Number(getMissingRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    return aMissingRatio - bMissingRatio;
  });
};
// 与GroupConfigVO.ts文件的computeResultGroupNumber方法重复
const computeResultGroupNumber = (rg: ResultGroupInfo) => {
  let rgUnmatchingParameterCount = 0;
  let rgWarningParameterCount = 0;
  let rgMatchingParameterCount = 0;
  // 未分析
  let rgMissingParameterCount = 0;
  let rgTotalParameterCount = 0;
  rg.results.forEach((r) => {
    if (r.unmatchingRecipeSteps && r.unmatchingRecipeSteps.length > 0) {
      rgUnmatchingParameterCount++;
    } else if (r.warningRecipeSteps && r.warningRecipeSteps.length > 0) {
      rgWarningParameterCount++;
    } else if (r.matchingRecipeSteps && r.matchingRecipeSteps.length > 0) {
      rgMatchingParameterCount++;
    } else if (r.missingRecipeSteps && r.missingRecipeSteps.length > 0) {
      rgMissingParameterCount++;
    }
    rgTotalParameterCount++;
  });
  // Calculate Column(GroupConfig) Unmatching Ratio
  rg.unmatchingCount = rgUnmatchingParameterCount;
  rg.warningCount = rgWarningParameterCount;
  rg.matchingCount = rgMatchingParameterCount;
  rg.missingCount =
    rgTotalParameterCount -
    (rgUnmatchingParameterCount + rgWarningParameterCount + rgMatchingParameterCount);
  rg.totalCount = rgTotalParameterCount;
};
const filterParameterAndStep = (
  results: any[],
  categoryCodeIds: string[],
  searchContent: string
) => {
  let count = 0;
  return results
    .map((deepOne) => {
      // 存在参数过滤或第一次进来的时候没有选择参数
      if (filterParameterByCategory(deepOne, categoryCodeIds, searchContent)) {
        count++;
        deepOne.isUnMatchingTop5 = count <= 5;
        return deepOne;
      }
      return null;
    })
    .filter((deepOne) => deepOne !== null);
};
const filterResultGroupInfos = (
  resultGroupInfo: ResultGroupInfo,
  categoryCodeIds: string[],
  searchContent: string
) => {
  resultGroupInfo.results = filterParameterAndStep(
    resultGroupInfo.results,
    categoryCodeIds,
    searchContent
  );
  resultGroupInfo.setUnMatchingTop5();
  // 计算match unMatch warn的数量
  computeResultGroupNumber(resultGroupInfo);

  return resultGroupInfo;
};
// 计算第二行数据match匹配率
const computedRow2DataForSearch = (
  columnData: any[],
  tempOneTwoLines: any[],
  categoryCodeIds: string[],
  searchContent: string
): any[] => {
  const columns = cloneDeep(columnData);
  const matchingInfo: MatchingInfo = {
    unmatchingCountAll: 0,
    warningCountAll: 0,
    matchingCountAll: 0,
    missingCountAll: 0,
    totalCountAll: 0
  };
  // 收集所有符合过滤条件的参数，避免重复计算
  const processedParameters = new Set<string>();

  // 处理数据，item.resultGroupInfos为分组数据，没有category信息
  columns?.forEach((item: GroupConfig) => {
    // 计算参数维度的匹配或者不匹配的数量
    if (item?.resultGroupInfo) {
      const firstItemGroupConfigId = item?.resultGroupInfo.groupConfigId;
      const tempInfo = item.resultGroupInfos?.filter(
        (group: any) => group?.groupConfigId === firstItemGroupConfigId
      );
      console.log('xxxxx tempInfo', tempInfo);

      // 只处理第一个分组的数据来避免重复计算
      if (tempInfo && tempInfo.length > 0) {
        const firstGroup = tempInfo[0];
        item.resultGroupInfo = filterResultGroupInfos(firstGroup, categoryCodeIds, searchContent);

        firstGroup.results.forEach((parameter: any) => {
          // 避免重复计算同一个参数
          if (processedParameters.has(parameter.parameterName)) {
            return;
          }
          processedParameters.add(parameter.parameterName);

          // 根据allColInfo的内容来确定单个参数是否为匹配或者不匹配
          // 按优先级判断参数状态：unmatching > warning > matching > missing
          let hasUnmatching = false;
          let hasWarning = false;
          let hasMatching = false;
          let hasMissing = false;

          // 遍历所有列的数据，收集该参数在所有列中的状态
          for (const [, value] of parameter.allColInfo) {
            if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
              hasUnmatching = true;
            }
            if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
              hasWarning = true;
            }
            if (value?.matchingRecipeSteps && value.matchingRecipeSteps.length > 0) {
              hasMatching = true;
            }
            if (value?.missingRecipeSteps && value.missingRecipeSteps.length > 0) {
              hasMissing = true;
            }
          }

          // 按优先级确定参数的最终状态并计数
          if (hasUnmatching) {
            matchingInfo.unmatchingCountAll += 1;
          } else if (hasWarning) {
            matchingInfo.warningCountAll += 1;
          } else if (hasMatching) {
            matchingInfo.matchingCountAll += 1;
          } else if (hasMissing) {
            matchingInfo.missingCountAll += 1;
          } else {
            // 如果没有任何步骤信息，默认计为missing
            matchingInfo.missingCountAll += 1;
          }

          // 每个参数都计入总数
          matchingInfo.totalCountAll += 1;
        });

        // 对所有分组应用过滤
        item.resultGroupInfos = item.resultGroupInfos?.map((group) =>
          filterResultGroupInfos(group, categoryCodeIds, searchContent)
        );
      }
    }
  });
  // 修改match unMatch warn统计值
  tempOneTwoLines[1].matchingInfo = matchingInfo;
  // 根据match值排序
  sortResultInfo(columns);
  tempOneTwoLines[0].configColumns = columns;

  return columns;
};
