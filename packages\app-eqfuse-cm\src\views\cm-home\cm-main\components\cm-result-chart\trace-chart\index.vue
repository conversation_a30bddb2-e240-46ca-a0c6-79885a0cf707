<script setup lang="ts">
import { type ChartOptions, XChartVue, XChart, type AlignedData } from '@xchart/vue';
import {
  reactive,
  ref,
  watch,
  shallowRef,
  nextTick,
  inject,
  type Ref,
  onMounted,
  computed
} from 'vue';
import { useSelfFullScreen } from '@/utils/tools';
import { t } from '@futurefab/vxe-table';
import { CM_CHART_DEFUALT_FONT_SIZE, CM_CHART_DEFUALT_Y_MAX_TICK, TRACE_CHART_X_AXIS_TYPE } from '@/constant/charts';
import { dealYSpace, formatToXChart } from '@/utils';
import { useChartDomain } from '@futurefab/ui-sdk-ees-charts';
import ToolBar from './tool-bar.vue';
// import { CopyChartType } from '../interface';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import {
  getTraceOptions,
  getViewDataTableOptions,
  getFilterFieldList,
  formatToTable,
  setNormalization,  
} from './config';
import viewDataTable from '../components/view-data-table/index.vue';
import { cloneDeep, debounce } from 'lodash-es';
import { useBaseStore, type LineItem, type NewChartConfig } from '@futurefab/ui-sdk-stores';
import type { CmFilterObject } from '../interface';
import { useCmChartLegendStore, useCustomLegendStore } from '@futurefab/ui-sdk-stores';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import CmChartCollapse from '@/components/cm-chart-collapse/index.vue';
import CmChartCopy from '@/components/cm-chart-copy/index.vue';
import { useGroupLegend } from '../useGroupLegend';
import { useResizeObserver } from '@vueuse/core';
import type { CustomLegendData } from '../components/custom-legend/interface';
const chartConfigStore = useCmChartLegendStore();
const baseStore = useBaseStore();
const customLegendStore = useCustomLegendStore();
export interface Props {
  chartData: any;
  groupData: any;
  boxDetail: {
    commonGroupConfigVO: GroupConfigVO;
    useDataNormalization: boolean;
    traceMatchingType: string;
  };
  currentRowInfo: string[];
  ids: number[];
  contextIndex: any;
  metaData: any;
  tableColumns?: any[];
  stepInfo?: any[];
  websocketParams?: any;
  splitLegend?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  chartData: null,
  groupData: null,
  boxDetail: () => ({
    commonGroupConfigVO: new GroupConfigVO(),
    useDataNormalization: false,
    traceMatchingType: 'ALL'
  }),
  currentRowInfo: () => [],
  ids: () => [],
  contextIndex: {},
  metaData: {},
  stepInfo: () => [],
  websocketParams: () => ({}),
  splitLegend: true
});
const emit = defineEmits(['changeColumn']);
const groupColumns = computed(() => props.tableColumns?.length ? props.tableColumns.slice(1) : []); 
const filterStep = ref<string[]>([]);
const open = defineModel<boolean>('open', { default: true });
const copyChartConfig = reactive({
  id: 'trace-chart' + Math.random(),
  name: `Trace Chart，${props?.chartData?.[0]?.paramAlias}`
});
const chartLoading = ref<boolean>(false);

const toolBarRef = ref();
let xChartDOM = shallowRef<XChart>();
const oneChartData = reactive({
  xOption: {} as ChartOptions,
  data: [] as AlignedData,
  seriesColor: [] as string[],
  contextData: [] as any[]
});
// trace chart normalization checkbox
const normalization = ref<boolean>(false);
const matchRadio = ref<string>();
// trace chart xAxis change
const xAxisValue = ref<string>('step_count_slot');
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const isDeleteBlank = ref(false);
const showDeleteBlank = computed(() => xAxisValue.value === 'TIME');
// trace chart table
const viewDataRef = ref();
const viewTableData = ref<any>([]);
const viewData = () => {
  viewDataRef.value.show();
};
// chat draw
const traceRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(traceRef);
const autoY = ref<[number | null, number| null] | null>(null);
const filterInfo = ref<CmFilterObject>({
  type: 'Group',
  filterKeys: []
});
const filterLotWaferData = ref<any[]>([]);
const { customLegendItems, setGroupConfig, initLengend } = useGroupLegend(
  filterInfo,
  customLegendStore,
  filterLotWaferData
);
const handleLoading = (Loading: boolean) => {
  chartLoading.value = Loading;
};
const chartContentRef = ref();
const getChartSize = (fullscreen?: boolean) => {
  const width = chartContentRef.value && chartContentRef.value.offsetWidth - 1;
  const height = chartContentRef.value && chartContentRef.value.offsetHeight - 1;
  return { width, height };
};
const initChart = debounce(async () => {
  const cloneDeepChartData = cloneDeep(props.chartData);
  const { commonGroupConfigVO, useDataNormalization, traceMatchingType } = props.boxDetail;
  matchRadio.value = traceMatchingType;
  const { width, height } = getChartSize();
  // 数据处理
  viewTableData.value = formatToTable(cloneDeepChartData, groupColumns.value);
  let normalizationValue = [];
  if (normalization.value) {
    normalizationValue = await setNormalization(
      cloneDeepChartData,
      filterInfo.value,
      customLegendItems.value
    );
  }
  const groupConfigColor = groupColumns.value.map(
    ({ id, chartLegendColor, name }) => ({
      id,
      chartLegendColor,
      name
    })
  );
  const { xChartData, seriesColor, seriesLegend, xAxisMinMax, yAxisMinMax, tooltipData, newChartData, xMap, totalCount } =
    await formatToXChart({
      initChartData: cloneDeepChartData,
      xyKey: TRACE_CHART_X_AXIS_TYPE.find(({ value }) => value === xAxisValue.value)?.XYGroup || [
        'count_slot',
        'VALUE'
      ],
      groupConfigColor: groupConfigColor,
      normalizationValue: normalizationValue,
      filterInfo: filterInfo.value,
      chartLegendConfig: chartConfigStore.chartLegendConfig,
      customLegendItems: customLegendItems.value,
      isDeleteBlank: isDeleteBlank.value,
      filterLotWaferData: filterLotWaferData.value,
      filterStep: filterStep.value.length === props.stepInfo?.length ? [] : filterStep.value,
      customLegendData: customLegendStore.customLegendData,
    });
  oneChartData.data = xChartData;
  oneChartData.seriesColor = seriesColor;
  oneChartData.contextData = newChartData.map(
    ({ contextKey }: any) => {
      return { contextKey };
    }
  );

  oneChartData.xOption = await getTraceOptions({
    contextIndex: props.contextIndex,
    chartData: oneChartData,
    model: xAxisValue.value,
    groupData: props?.groupData,
    currentRowInfo: props?.currentRowInfo,
    xChartDOM,
    width,
    height,
    originData: newChartData,
    xAxisMinMax,
    yAxisMinMax,
    tooltipData,
    chartLegendConfig: chartConfigStore.chartLegendConfig as NewChartConfig,
    commonGroupConfigVO,
    overlay: false,
    isFullscreen: isFullscreen.value,
    isDeleteBlank: isDeleteBlank.value,
    xMap,
    filterStep: filterStep.value.length === props.stepInfo?.length ? [] : filterStep.value,
    highlightStep: highlightStep.value,
    isScreenshotMode: isScreenshotMode.value,
    groupColumns: groupColumns.value,
    customLeftYRange: autoY.value,
    seriesLegend,
    totalCount,
  });

  oneChartData.xOption.hooks!.setSeries = [
    (chart, seriesIdx, opts) => {
      if (!opts.show) {
        useChartDomain(chart, 'markData', true);
      }
      const lineWidth = chartConfigStore.chartLegendConfig?.lineWidth || 1;
      if (seriesIdx) {
        chart.series.forEach((s: { width: any }, i: any) => {
          s.width = i == seriesIdx ? lineWidth + 2 : lineWidth;
        });
      } else {
        chart.series.forEach((s: { width: any }) => {
          s.width = lineWidth;
        });
      }
    }
  ];
  oneChartData.xOption.hooks!.draw = [
    () => {
      useChartDomain(xChartDOM.value as any, 'markData', true);
    }
  ];
  handleLoading(false);
}, 300);
const isFullscreenChange = (fullscreen: boolean) => {
  nextTick(() => {
    const { width, height } = getChartSize(fullscreen);
    oneChartData.xOption.height = height;
    oneChartData.xOption.width = width;
    const fontSize = chartConfigStore.chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE;
    const maxYTick = (chartConfigStore.chartLegendConfig as any)?.yAxisMaxTicksLimit || CM_CHART_DEFUALT_Y_MAX_TICK;
    (oneChartData.xOption as any)!.axes[1].space = dealYSpace(height, maxYTick, fontSize)
  });
};

onMounted(() => {
  useResizeObserver(chartContentRef.value, () => {
    isFullscreenChange(isFullscreen.value);
  });
});
watch(
  [() => groupColumns.value, () => customLegendStore.customLegendData],
  () => {
    // overlay 修改 store 时 要同步 filterLotWaferData中的 customLegend 和 颜色
    const temp: LineItem[] = [];
    filterLotWaferData.value.forEach(row => {
      const storeItem = customLegendStore.customLegendData.lineItems.find(item => item.contextKey ===  row.contextKey);
      temp.push({...storeItem!})
    });
    filterLotWaferData.value = temp;
    setGroupConfig((groupColumns.value as any) || []);
    filterInfo.value = { type: 'Group', filterKeys: [] };
    nextTick(initChart());
  },
  { immediate: true }
);

watch(
  [normalization, xAxisValue, () => props.chartData, () => baseStore.theme],
  async () => {
    handleLoading(true);
    filterStep.value = [];
    filterLotWaferData.value = [];
    initChart();
    copyChartConfig.id = 'trace-chart' + Math.random();
    copyChartConfig.name = `Trace Chart，${props?.chartData?.[0]?.paramAlias}`;
    // 重置 yRange 设置
    autoY.value = null;
    toolBarRef.value?.resetYrange();
  },
  { immediate: true, flush: 'post' }
);

watch([() => chartConfigStore.chartLegendConfig], () => {
  initChart();
});

// Screenshot
const highlightStep = inject<Ref<boolean>>('highlightStep', ref(false));
const isScreenshotMode = inject<Ref<boolean>>('isScreenshotMode', ref(false));

const filterChange = (info: CmFilterObject) => {
  filterInfo.value = { ...info };
  initLengend();
  nextTick(initChart);
};
const legendChange = () => {
  handleLoading(true);
  initChart();
};
const clickToggle = () => {
  toggle();
};
const deleteBlankChange = () => {
  initChart();
};
const filterLotWafer = (event: any[]) => {
  filterLotWaferData.value = event;
  initLengend();
  initChart();
};
const filterStepChange = () => {
  initLengend();
  initChart();
};
const applyAutoY = (event: any) => {
  if (event) {
    autoY.value = [event.minLeftY, event.maxLeftY];
  } else {
    autoY.value = null
  }
  initChart();
};
watch(
  () => props.ids,
  () => {
    filterInfo.value = { type: 'Group', filterKeys: props.ids };
    filterLotWaferData.value = [];
    filterStep.value = [];
    initChart();
    initLengend();
    toolBarRef.value.setFilterGroup(props.ids);
  }
);
const changeColumn = (event: any) => emit('changeColumn', event);
defineExpose({
  highlight: (event: any) => {
    const index = oneChartData.contextData.findIndex((item) => item.contextKey === event.contextKey);
    if (index !== -1) {
      (xChartDOM.value as any)?.triggerSetSeries(index + 1);
    }
  }
});
</script>
<template>
  <div
    ref="traceRef"
    v-isLoading="{
      isShow: chartLoading,
      hasButton: false,
      title: 'Loading...'
    }"
    class="trace-chart"
  >
    <CmChartCollapse :is-full-screen="isFullscreen" v-model:open="open">
      <template #header>
        <div class="chart-top">
          <div class="title-box">
            <p
              :class="isFullscreen ? 'full-chart-title' : 'chart-title'"
              :title="props?.chartData?.[0]?.paramAlias"
            >
              {{ $t('cm.title.traceChart') }}
            </p>
            <vxe-tooltip
              :content="t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
              :use-h-t-m-l="true"
              theme="light"
            >
              <i
                class="iconfont chart_icon_icon zoom-icon"
                :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
                style="color: var(--text-hint-color)"
                @click="clickToggle"
              ></i>
            </vxe-tooltip>
            <a-select
              v-model:value="filterStep"
              show-search
              placeholder="Step Filter"
              :filter-option="filterOption"
              mode="multiple"
              max-tag-count="responsive"
              style="min-width: 118px; width: 220px"
              :show-arrow="true"
              allow-clear
              @change="filterStepChange"
            >
              <a-select-option v-for="{ step, color } in stepInfo" :key="step" :value="step">
                <span :style="`color: ${color}`">{{ step }}</span>
              </a-select-option>
            </a-select>
          </div>
          <div class="right-setting">
            <a-select
              v-model:value="xAxisValue"
              show-search
              placeholder="Select trace aXis"
              style="width: 290px"
              :options="TRACE_CHART_X_AXIS_TYPE"
              :show-arrow="true"
              :filter-option="filterOption"
            ></a-select>
            <a-space class="normalization">
              <span class="label"> Normalization </span>
              <a-switch v-model:checked="normalization"></a-switch>
            </a-space>
            <tool-bar
              ref="toolBarRef"
              v-model:is-delete-blank="isDeleteBlank"
              :show-delete-blank="showDeleteBlank"
              :common-group-config-v-o="props?.boxDetail?.commonGroupConfigVO"
              :copy-chart-config="copyChartConfig"
              :ids="props.ids"
              :chart-data="chartData"
              :metaData="metaData"
              :tableColumns="tableColumns"
              :websocketParams="websocketParams"
              @view-data="viewData"
              @filterChange="filterChange"
              @delete-blank="deleteBlankChange"
              @filterLotWafer="filterLotWafer"
              @changeColumn="changeColumn"
              @applyAutoY="applyAutoY"
            />
          </div>
        </div>
      </template>
      <template #content>
        <div class="chart-content">
          <CmChartCopy>
            <CmGroupLegend
              :legend="customLegendItems"
              :title="props?.chartData?.[0]?.paramAlias"
              :split-lenged="props.splitLegend"
              @legendChange="legendChange"
            >
              <template #chart>
                <div ref="chartContentRef" :id="copyChartConfig.id" class="xchart-draw">
                  <XChartVue
                    v-if="open"
                    :options="oneChartData.xOption"
                    :data="oneChartData.data"
                    @create="(chart: any) => (xChartDOM = chart)"
                  />
                </div>
              </template>
            </CmGroupLegend>
          </CmChartCopy>
        </div>
      </template>
    </CmChartCollapse>
  </div>
  <Teleport to="body">
    <view-data-table
      ref="viewDataRef"
      :data="viewTableData"
      :options="getViewDataTableOptions()"
      :column-field-list="getFilterFieldList()"
    ></view-data-table>
  </Teleport>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.trace-chart {
  height: 100%;
  width: 100%;
  background-color: @bg-block-color;
  padding: 0;
  border-radius: 4px;
  border-bottom: 1px solid @border-color;
}
.chart-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  // overflow: auto;
  align-items: center;
  // 显示滚动条
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: @bg-scroll-color;
    }
  }
  &::-webkit-scrollbar-corner {
    background-color: @bg-scroll-color;
  }
  .title-box {
    display: flex;
    align-items: center;
    p {
      font-size: 12px;
      font-weight: bold;
    }
    .zoom-icon {
      margin: 0 0 0 5px;
      color: @text-title-color;
      width: 20px;
      height: 20px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      &:hover {
        cursor: pointer;
        color: @text-weak-text;
        background-color: @bg-hover-2-color;
      }
    }
    .title-like-icon {
      display: inline-block;
      background: @primary-color;
      width: 7px;
      height: 14px;
      border-radius: 0 7px 7px 0;
      line-height: 40px;
      margin-right: 8px;
    }
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      line-height: 22px;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
      color: @text-title-color;
    }
    .full-chart-title {
      font-size: 16px;
      font-weight: bold;
      line-height: 22px;
      display: inline-block;
      white-space: nowrap;
      margin: 0;
      color: @text-title-color;
    }
  }
  :deep(.right-setting) {
    display: flex;
    flex-direction: row;
    margin: 0;
    .normalization {
      color: @text-title-color;
      margin: 0 10px;
    }
  }
}
.chart-content {
  height: 100%;
}
.xchart-draw {
  height: 100%;
  width: 100%;
  :deep(.xchart-toolbox) {
    display: none;
  }
  // 将十字架光标变为鼠标
  :deep(.xchart.zooming .xchart-over:hover) {
    cursor: default;
  }
  :deep(.xchart.can-zoom-back) {
    cursor: default;
  }
}
</style>
