<script setup lang="ts">
import { reactive, defineProps, watch, ref } from 'vue';
import { Form } from '@futurefab/ant-design-vue';
import { t } from '@futurefab/vxe-table';
import dayjs from 'dayjs';
import { EesCustomTime } from '@futurefab/ui-sdk-ees-basic';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import { apiConfig, getEesDbWaferData } from '@futurefab/ui-sdk-api';
import { cmMainInfo as buttonIds } from '@/log-config';

const props = withDefaults(
  defineProps<{
    commonGroupConfig: any;
  }>(),
  {
    commonGroupConfig: {}
  }
);
const emits = defineEmits(['handleRefresh', 'resetCanGetOptions', 'setWaferData']);
const runByModel = defineModel('runByModel');
const groupLoading = defineModel('groupLoading');

interface FormState {
  fromToType: string;
  relativeTime: number;
  relativeUnit: string;
  eqpModuleIds: string[];
  attributeConfigs: any[];
  rangeTime: string[];
  loop: boolean;
  productIds: any[];
  recipeId: string;
  routesIds: any[];
  // form label
  EQP: string;
  Chamber: string;
  Recipe: string;
}
const useForm = Form.useForm;
const initData = {
  attributeConfigs: props.commonGroupConfig?.attributeConfigs || [],
  fromToType: props.commonGroupConfig?.fromToType || 'relative',
  relativeTime: props.commonGroupConfig?.relativeTime || 8,
  relativeUnit: props.commonGroupConfig?.relativeUnit || 'hours',
  rangeTime: [
    props.commonGroupConfig?.from || dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    props.commonGroupConfig?.to || dayjs().format('YYYY-MM-DD HH:mm:ss')
  ],
  eqpModuleIds: [],
  loop: false,
  productIds: [],
  recipeId: '',
  routesIds: [],
  // form label
  EQP: 'EQP',
  Chamber: 'Chamber',
  Recipe: 'Recipe'
};
const dbFromRef = reactive<FormState>(initData);
const timeAreaBind = ref<any>([dayjs().subtract(7, 'day'), dayjs()]);
const timePop = (val: any) => {
  if (val?.length > 0) {
    dbFromRef['rangeTime'] = [
      dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'),
      dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss')
    ];
  }
};
const fromToTypeOptions = [
  { label: t('cm.label.relative'), value: 'relative' },
  { label: t('cm.label.specific'), value: 'specific' }
];
const relativeUnitOptions = [
  { label: t('cm.label.hours'), value: 'hours' },
  { label: t('cm.label.days'), value: 'days' }
];
// dataset-form下拉框内容change
const selectChange = (value: any, alias: any) => {
  let flag = false;
  let flagDisabled = 0;
  if ((value && value.length === 0) || !value) {
    props.commonGroupConfig.selectedAttributeConfigs.forEach((e: any, i: number) => {
      if (e.alias === alias) {
        e.checkAll = false;
        flagDisabled = i;
      }
    });
    props.commonGroupConfig.attributeConfigs.forEach((e: any, i: number) => {
      if (i > flagDisabled) {
        e.disabled = true;
        e.loadedData = false;
        e.options = [];
      }
    });
  } else {
    props.commonGroupConfig.selectedAttributeConfigs.forEach((e: any, i: number) => {
      if (e.alias === alias) {
        e.checkAll = value.length === e.options.length;
        flagDisabled = i;
      }
    });
    props.commonGroupConfig.attributeConfigs.forEach((e: any, i: number) => {
      if (i - flagDisabled === 1) {
        e.disabled = false;
        e.loadedData = false;
        e.options = [];
        e.dropdownVisibleChange(true, e);
      }
    });
  }
  props.commonGroupConfig.attributeConfigs.forEach((e: any, i: number) => {
    if (flag) {
      e.checkAll = false;
      e.loadedData = false;
      e.value = [];
    }
    if (e.alias === alias) {
      flag = true;
    }
  });
  props.commonGroupConfig.setSelectedAttributeConfigs(props.commonGroupConfig.attributeConfigs);
};
const handleRefresh = () => {
  emits('handleRefresh');
};
// dataset-form单个下拉全选
const changeAttr = (i: any) => {
  if (i.checkAll) {
    i.value = i.options.map((e: any) => e.value);
    selectChange(i.value, i.title);
  } else {
    i.value = [];
  }
};
// 查询wafer数据
const getDatabaseWaferData = async () => {
  groupLoading.value = true;
  let eqpModuleIds: string[] = [];
  const allLocations = props.commonGroupConfig.getAllLocations();
  const thisChamberIds = props.commonGroupConfig.getChamberIds();
  const { toolLists, chamberLists, routesIds, recipeIds, productIds } =
    props.commonGroupConfig.getAllSelectData();
  let chamberValues = [];
  if (chamberLists.length > 0) {
    chamberValues = chamberLists;
  } else {
    chamberValues = thisChamberIds;
  }
  eqpModuleIds = props.commonGroupConfig.setEqpModuleIds(chamberValues, toolLists, allLocations);
  let from: any = '';
  let to: any = '';
  if (props.commonGroupConfig.fromToType === 'specific') {
    from = dayjs(props.commonGroupConfig?.from).valueOf();
    to = dayjs(props.commonGroupConfig?.to).valueOf();
  } else {
    if (props.commonGroupConfig.relativeUnit === 'hours') {
      from = dayjs().subtract(props.commonGroupConfig.relativeTime, 'hour').valueOf();
    } else {
      from = dayjs().subtract(props.commonGroupConfig.relativeTime, 'day').valueOf();
    }
    to = dayjs().valueOf();
  }
  const waferFileList = await getEesDbWaferData({
    bodyParams: {
      from,
      to,
      eqpModuleIds,
      routesIds,
      recipeIds,
      // recipeId: recipeIds[0],
      productIds,
      loop: runByModel.value === 'loop'
    },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-ees-db-wafer-data'
    }
  });
  if (waferFileList.status === 'SUCCESS') {
    waferFileList.data.data = waferFileList.data?.data?.map((item: any) => ({
      ...item,
      wafer: item?.waferId?.split(';')?.[0]
    }));
    const tempData = waferFileList.data;
    emits('setWaferData', tempData);
    datasetGroupStore.handleClear();
  }

  groupLoading.value = false;
};
// 初始化数据
const initModelData = () => {
  Object.assign(dbFromRef, initData);
};
// 监听时间变化，修改from和to的值
watch(
  [
    () => dbFromRef.fromToType,
    () => dbFromRef.relativeTime,
    () => dbFromRef.relativeUnit,
    () => dbFromRef.rangeTime
  ],
  () => {
    emits('resetCanGetOptions');
    props.commonGroupConfig.setTimeInfo('fromToType', dbFromRef.fromToType);
    if (dbFromRef.fromToType === 'relative') {
      props.commonGroupConfig.setTimeInfo('relativeTime', dbFromRef.relativeTime);
      props.commonGroupConfig.setTimeInfo('relativeUnit', dbFromRef.relativeUnit);
    } else if (dbFromRef.rangeTime?.length > 1) {
      props.commonGroupConfig.setTimeInfo('from', dayjs(dbFromRef.rangeTime[0]));
      props.commonGroupConfig.setTimeInfo('to', dayjs(dbFromRef.rangeTime[1]));
    }
  }
);
watch(
  props.commonGroupConfig,
  () => {
    initModelData();
  },
  { immediate: true }
);

defineExpose({ getDatabaseWaferData });
</script>

<template>
  <div class="dataset-from">
    <a-form :model="dbFromRef" :label-align="'left'" name="datasetForm">
      <a-form-item name="fromToType" label="Time" :colon="false" :required="false">
        <a-radio-group
          v-model:value="dbFromRef.fromToType"
          size="small"
          :options="fromToTypeOptions"
        />
      </a-form-item>
      <a-form-item
        v-show="dbFromRef.fromToType === 'relative'"
        label=""
        :colon="false"
        :required="false"
        layout="inline"
        name="relativeTime"
      >
        <div class="dataset-from-time">
          <a-input-number v-model:value="dbFromRef.relativeTime">
            <template #addonBefore>
              <p class="special-label">Last</p>
            </template>
            <template #addonAfter>
              <a-select
                class="special-label"
                v-model:value="dbFromRef.relativeUnit"
                :options="relativeUnitOptions"
                :show-arrow="true"
              />
            </template>
          </a-input-number>
          <a-button @click="handleRefresh">
            {{ $t('common.btn.refresh') }}
          </a-button>
        </div>
      </a-form-item>
      <a-form-item
        v-show="dbFromRef.fromToType === 'specific'"
        name="rangeTime"
        :colon="false"
        label=""
        :required="false"
      >
        <div class="dataset-from-time">
          <EesCustomTime
            ref="refTime"
            :time-area="timeAreaBind"
            :max-period="null"
            :show-title="false"
            :api-config="apiConfig"
            :isColumn="false"
            :bordered="true"
            @time-pop="timePop"
          ></EesCustomTime>
          <a-button @click="handleRefresh">
            {{ $t('common.btn.refresh') }}
          </a-button>
        </div>
      </a-form-item>
      <a-form-item
        v-for="(i, index) in commonGroupConfig?.attributeConfigs"
        :key="index"
        :name="i.title"
        :label="i.title"
        :colon="false"
        :required="false"
      >
        <a-select
          v-model:value="i.value"
          show-search
          allow-clear
          :mode="i.multiSelection ? 'multiple' : ''"
          :options="i.options"
          :disabled="i.disabled"
          :loading="i.loadedData && i.options?.length === 0"
          :show-arrow="true"
          @dropdown-visible-change="(open: any) => i.dropdownVisibleChange(open, i)"
          @change="(value: any) => selectChange(value, i.title)"
        >
          <template #clearIcon>
            <i
              class="iconfont icon-close"
              style="font-size: 15px; color: var(--bg-icon-active-color)"
            />
          </template>
          <template
            v-if="i.multiSelection && i.options?.length > 0"
            #dropdownRender="{ menuNode: menu }"
          >
            <div style="padding: 5px">
              <a-checkbox v-model:checked="i.checkAll" @change="changeAttr(i)">
                {{ i.checkAll ? 'Uncheck All' : 'Check All' }}
              </a-checkbox>
            </div>
            <component :is="menu"></component>
          </template>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.dataset-from {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  overflow: auto;
  :deep(.ant-form-item-label) {
    min-width: 106px;
  }
  .dataset-from-time {
    margin-left: 106px;
    :deep(.ant-input-number-group-wrapper) {
      display: flex;
      flex: 1;
    }
    :deep(.ant-input-number) {
      height: 32px;
    }
    .special-label {
      color: @text-hint-color;
      min-width: 85px;
    }
    :deep(.ant-select .ant-select-selector) {
      border: solid 1px @border-color !important;
      background-color: @bg-group-color !important;
    }
    :deep(.ant-select-selection-item) {
      color: @text-hint-color !important;
    }
    :deep(.custom-time-wrap-row) {
      flex: 1;
    }
  }
  :deep(.ant-form-item) {
    margin-bottom: 10px;
    &-row {
      display: flex;
      justify-content: space-between;
    }
  }
  &-time {
    display: flex;
    flex-direction: row;
    gap: 10px;
  }
  // disabled状态下，下拉图标颜色不一样
  :deep(.ant-select-disabled .ant-select-arrow) {
    color: @bg-icon-disable-color;
  }
  // 调整图标位置
  :deep(.ant-select-arrow) {
    top: 15px;
  }
  :deep(.ant-select-clear) {
    top: 15px;
  }
}
</style>
