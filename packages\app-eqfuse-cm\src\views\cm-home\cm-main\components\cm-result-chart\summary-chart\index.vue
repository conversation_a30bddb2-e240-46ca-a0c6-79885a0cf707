<!-- eslint-disable @typescript-eslint/no-non-null-assertion -->
<script setup lang="ts">
import { type ChartOptions, XChartVue, XChart } from '@xchart/vue';
import {
  reactive,
  ref,
  watch,
  shallowRef,
  onUnmounted,
  onMounted,
  inject,
  type Ref,
  nextTick,
  computed
} from 'vue';
import { useSelfFullScreen } from '@/utils/tools';
import ToolBar from './tool-bar.vue';
import { useChartDomain } from '@futurefab/ui-sdk-ees-charts';
import { getSummaryOptions } from './config';
import { getSummaryChartFormatData } from '@/utils';
import { getViewDataTableOptions, getSummaryChartTableData, getFilterFieldList } from './config';
import ViewDataTable from '../components/view-data-table/index.vue';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { debounce } from 'lodash-es';
import { useBaseStore, type NewChartConfig } from '@futurefab/ui-sdk-stores';
import type { CmFilterObject } from '../interface';
import { throttle } from 'lodash-es';
import { useCmChartLegendStore, useCustomLegendStore } from '@futurefab/ui-sdk-stores';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import { useGroupLegend } from '../useGroupLegend';
import CmChartCollapse from '@/components/cm-chart-collapse/index.vue';
import CmChartCopy from '@/components/cm-chart-copy/index.vue';
import { useResizeObserver } from '@vueuse/core';
const chartConfigStore = useCmChartLegendStore();
const baseStore = useBaseStore();
const customLegendStore = useCustomLegendStore();
export interface Props {
  activeStep: string;
  chartData: any;
  commonGroupConfigVO: GroupConfigVO;
  ids: number[];
  paramAlias: string;
  contextIndex: any;
  metaData: any;
  tableColumns?: any[];
  splitLegend?: boolean;
}
const emits = defineEmits(['pointClick']);
const groupColumns = computed(() => props.tableColumns?.length ? props.tableColumns.slice(1) : []); 
const toolBarRef = ref();
const open = defineModel<boolean>('open', { default: true });
const props = withDefaults(defineProps<Props>(), {
  activeStep: '',
  chartData: null,
  commonGroupConfigVO: () => new GroupConfigVO(),
  ids: () => [],
  contextIndex: [],
  metaData: {},
  splitLegend: true
});
let xChartDOM = shallowRef<XChart>();
const oneChartData = reactive({
  xOption: {} as ChartOptions,
  data: [],
  seriesColor: [] as string[],
  seriesGroupId: [] as number[]
});
// 监听算法模式
const stateValue = defineModel<string>('stateValue');
const changeState = (state: string) => {
  stateValue.value = state;
};
const copyChartConfig = reactive({
  id: 'summary-chart' + Math.random(),
  name: `Summary Chart，Recipe Step：${Math.floor(Number(props.activeStep))}，${
    props?.paramAlias
  }，Stat Type：${stateValue.value}`
});
// view data
const viewTableData = ref<any[]>([]);
const viewDataRef = ref();
const viewData = () => {
  viewDataRef.value.show();
};
// chart draw
const summaryRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(summaryRef);

const filterInfo = ref<CmFilterObject>({ type: 'Group', filterKeys: [] });
const filterChart = (info: any) => {
  filterInfo.value = { ...info };
  initLengend();
  nextTick(initChart);
};
const isDeleteBlank = ref(true);
const { customLegendItems, setGroupConfig, initLengend } = useGroupLegend(filterInfo, customLegendStore);
const chartBoxWidth = defineModel<number>('chartBoxWidth');
const chartBoxHeight = defineModel<number>('chartBoxHeight');
const chartContentRef = ref();
const initChart = debounce(() => {
  const width = chartContentRef.value && chartContentRef.value.offsetWidth - 1;
  const height = chartContentRef.value && chartContentRef.value.offsetHeight;
  const groupConfigColor = groupColumns.value.map(
  ({ id, chartLegendColor, name }) => ({
      id,
      chartLegendColor,
      name
    })
  );
  const { coordinate, seriesColor, xAxisMinMax, yAxisMinMax, xMap, seriesGroupId } =
    getSummaryChartFormatData({
      data: props.chartData,
      keysArr: ['START_TIME', stateValue.value, 'context'],
      groupConfigColor,
      step: props.activeStep,
      filterInfo: filterInfo.value,
      contextIndex: props.contextIndex,
      chartLegendConfig: chartConfigStore.chartLegendConfig,
      customLegendItems: customLegendItems.value,
      deleteBlank: isDeleteBlank.value,
      customLegendData: customLegendStore.customLegendData
    });
  viewTableData.value = getSummaryChartTableData(
    props.chartData,
    props.activeStep || '7',
    props.commonGroupConfigVO,
    props.contextIndex,
    groupColumns.value
  );
  if (coordinate.length > 0) {
    oneChartData.data = [null, ...coordinate] as never;
  } else {
    oneChartData.data = [];
  }
  oneChartData.seriesColor = seriesColor;
  oneChartData.seriesGroupId = seriesGroupId;
  oneChartData.xOption = getSummaryOptions({
    chartData: oneChartData,
    StatType: stateValue.value!,
    xChartDOM,
    width,
    height,
    commonGroupConfigVO: props.commonGroupConfigVO,
    xAxisMinMax,
    yAxisMinMax,
    contextIndex: props.contextIndex,
    chartLegendConfig: chartConfigStore.chartLegendConfig as NewChartConfig,
    isDeleteBlank: isDeleteBlank.value,
    xMap
  });
  oneChartData.xOption.hooks!.setSeries = [
    (chart, i, opts) => {
      if (!opts.show) {
        useChartDomain(chart, 'markData', true);
      }
    }
  ];
  oneChartData.xOption.hooks!.draw = [
    () => {
      useChartDomain(xChartDOM.value as any, 'markData', true);
    }
  ];
  oneChartData.xOption.hooks!.pointClick = [
    (self: any, e: any, { x, y, sidx, didx }: any) => {
      const contextKey = oneChartData.data[sidx][2][didx];
      emits('pointClick', { contextKey });
    }
  ];
}, 300);
watch(
  [
    stateValue,
    isFullscreen,
    () => baseStore.theme,
    chartBoxWidth,
    chartBoxHeight,
    () => chartConfigStore.chartLegendConfig
  ],
  () => {
    copyChartConfig.id = 'summary-chart' + Math.random();
    copyChartConfig.name = `Summary Chart，Recipe Step：${Math.floor(
      Number(props.activeStep)
    )}，${props?.paramAlias}，Stat Type：${stateValue.value}`;
    initChart();
  },
  {
    immediate: true,
    flush: 'post'
  }
);
watch([() => props.activeStep, () => props.chartData], () => {
  setGroupConfig((groupColumns.value as any) || []);
  filterInfo.value = { type: 'Group', filterKeys: [] };
  nextTick(initChart());
});
watch([() => groupColumns.value, () => customLegendStore.customLegendData], () => {
  setGroupConfig((groupColumns.value as any) || []);
  filterInfo.value = { type: 'Group', filterKeys: [] };
  nextTick(initChart());
});
onMounted(() => {
  setGroupConfig((groupColumns.value as any) || []);

  useResizeObserver(chartContentRef.value, () => {
    initChart();
  });
});

const legendChange = () => {
  initChart();
};
const deleteBlankChange = () => {
  initChart();
};

const title = computed(
  () => `${props.paramAlias} Step:${props.activeStep} Type:${stateValue.value}`
);
watch(
  () => props.ids,
  () => {
    filterInfo.value = { type: 'Group', filterKeys: props.ids };
    initChart();
    initLengend();
    toolBarRef.value.setFilterGroup(props.ids);
  }
);
</script>

<template>
  <div ref="summaryRef" class="summary-box">
    <CmChartCollapse :is-full-screen="isFullscreen" v-model:open="open">
      <template #header>
        <tool-bar
          ref="toolBarRef"
          v-model:is-delete-blank="isDeleteBlank"
          v-model:is-fullscreen="isFullscreen"
          v-model:state-value="stateValue"
          :ids="ids"
          :copy-chart-config="copyChartConfig"
          :common-group-config-v-o="props.commonGroupConfigVO"
          :metaData="metaData"
          :tableColumns="tableColumns"
          @toggle="toggle"
          @view-data="viewData"
          @change-state="changeState"
          @filterChange="filterChart"
          @delete-blank="deleteBlankChange"
        >
          <template #afterTitle>
            <slot name="afterTitle"></slot>
          </template>
        </tool-bar>
      </template>
      <template #content>
        <div class="chart-content" id="cm-summary-chart">
          <CmChartCopy>
            <CmGroupLegend
              :legend="customLegendItems"
              :title="title"
              :split-lenged="props.splitLegend"
              @legendChange="legendChange">
              <template #chart>
                <div ref="chartContentRef" :id="copyChartConfig.id" class="xchart-draw">
                  <XChartVue
                    v-if="open"
                    :options="oneChartData.xOption"
                    :data="oneChartData.data"
                    @create="(chart: any) => (xChartDOM = chart)"
                  />
                </div>
              </template>
            </CmGroupLegend>
          </CmChartCopy>
        </div>
      </template>
    </CmChartCollapse>
  </div>
  <Teleport to="body">
    <view-data-table
      ref="viewDataRef"
      :data="viewTableData"
      :options="getViewDataTableOptions()"
      :column-field-list="getFilterFieldList()"
    ></view-data-table>
  </Teleport>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.summary-box {
  background-color: @bg-block-color;
  height: 100%;
  width: 100%;
  padding: 0;
  border-radius: 4px;
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
  .chart-content {
    height: 100%;
    width: 100%;
  }
  .xchart-draw {
    height: 100%;
    width: 100%;
    margin-top: 10px;
    :deep(.xchart-toolbox) {
      display: none;
    }
    // 将十字架光标变为鼠标
    :deep(.xchart.zooming .xchart-over:hover) {
      cursor: default;
    }
    :deep(.xchart.can-zoom-back) {
      cursor: default;
    }
  }
}
</style>
