<script lang="ts" setup>
import { t } from '@futurefab/vxe-table';
import { ref, watch, reactive } from 'vue';
import { uuid } from '@futurefab/ui-sdk-ees-basic';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import type { NewGroupLegendConfig, NewChartConfig } from './interface';
import colorSelect from '@/components/color-select/index.vue';
import { useCmChartLegendStore } from '@futurefab/ui-sdk-stores';
import { CM_CHART_DEFUALT_FONT_SIZE, CM_CHART_DEFUALT_Y_MAX_TICK } from '@/constant/charts';
import { updateGroupNameAndColor } from '@futurefab/ui-sdk-api';
import { message } from '@futurefab/ant-design-vue';
const legendStore = useCmChartLegendStore();
const props = withDefaults(
  defineProps<{
    disabled?: boolean;
    groupColumns?: { id: number; key: string; color: string }[];
    websocketParams?: any;
  }>(),
  {
    disabled: false,
    groupConfigs: () => [],
    websocketParams: () => ({}),
  }
);

const emits = defineEmits(['changeColumn', 'close']);

const getInitialLineWidth = () => {
  return legendStore.chartLegendConfig?.lineWidth || 1;
};

const getInitialFontSize = () => {
  return legendStore.chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE;
};

const getInitialMaxTick = () => {
  return legendStore.chartLegendConfig?.yAxisMaxTicksLimit || CM_CHART_DEFUALT_Y_MAX_TICK;
};

const open = ref(false);
const data = reactive<NewChartConfig>({
  groupLegendConfigs: [],
  lineWidth: getInitialLineWidth(),
  coordinateFontSize: getInitialFontSize(),
  yAxisMaxTicksLimit: getInitialMaxTick()
});

watch(
  () => props.groupColumns,
  () => {
    const temp = props.groupColumns?.filter(item => item.id !== null && item.id !== undefined).map((item) => ({
      name: item.key,
      originName: item.key,
      color: item.color,
      originColor: item.color,
      id: item.id
    })) || [];

    data.groupLegendConfigs = temp;
  },
  { immediate: true, deep: true }
);

const uid = '' + uuid();
const onClose = () => {
  emits('close');
  open.value = false;
};

const selectColor = (item: NewGroupLegendConfig, color: string) => {
  item.color = color;
};

const apply = async () => {
  const params: any[] = [];
  const changeMap: any = {};
  data.groupLegendConfigs.forEach(item => {
    if (item.color !== item.originColor || item.name !== item.originName) {
      params.push({
        requestId: props.websocketParams.requestId,
        requestDt: props.websocketParams.requestDt,
        groupId: String(item.id),
        groupNameAlias: item.name || item.originName,
        groupColor: item.color || item.originColor,
      });
      changeMap[String(item.id)] = {
        groupNameAlias: item.name || item.originName,
        groupColor: item.color || item.originColor,
      }
    }
  });
  const tempFn = () => {
    legendStore.SET_CM_CHART_LEGEND_CONFIG({
      lineWidth: data.lineWidth,
      coordinateFontSize: data.coordinateFontSize,
      yAxisMaxTicksLimit: data.yAxisMaxTicksLimit,
    });
    open.value = false;
  }
  if (params.length) {
    try {
      const res = await updateGroupNameAndColor({
        bodyParams: params
      });
      if (res.status === 'SUCCESS') {
        message.success(t('cm.tips.modifySuccess'));
        // 触发修改 table 的 column
        emits('changeColumn', changeMap);
        tempFn();
      }
    } catch (error) {
      console.log('error:', error);
    }
  } else {
    tempFn();
  }
};
const lineWidthChange = (event: any) => {
  if (!data.lineWidth) {
    data.lineWidth = 1;
  }
};
const fontSizeChange = (event: any) => {
  if (!data.coordinateFontSize) {
    data.coordinateFontSize = 12;
  }
};
const openChange = () => {
  data.lineWidth = getInitialLineWidth();
  data.coordinateFontSize = getInitialFontSize();
  data.yAxisMaxTicksLimit = getInitialMaxTick();
};
</script>
<template>
  <div class="trend-chart-config-new">
    <a-popover
      v-model:open="open"
      :auto-adjust-overflow="true"
      placement="bottomRight"
      trigger="click"
      :id="uid"
      :overlay-class-name="'trend-chart-config-new-popover'"
      @openChange="openChange"
    >
      <ees-button-tip
        icon="#icon-btn-set"
        :text="t('cm.title.chartConfig')"
        :is-active="false"
        :is-border="true"
        :margin-right="10"
        :disabled="disabled"
      />
      <template #title>{{ t('cm.title.chartConfig') }}</template>
      <template #content>
        <div class="trend-chart-config-new-box" style="width: 648px">
          <div class="trend-chart-config-new-legend-box">
            <div class="trend-chart-config-new-legend-head">{{ 'Legend' }}</div>
            <div class="trend-chart-config-new-legend-form">
              <a-input
                v-model:value="item.name"
                v-for="item in data.groupLegendConfigs"
                :key="item.id"
                class="trend-chart-config-new-legend-form-item"
              >
                <template #suffix>
                  <colorSelect
                    :curColor="item.color"
                    @select-color="selectColor(item, $event)"
                  ></colorSelect>
                </template>
              </a-input>
            </div>
          </div>
          <div class="trend-chart-config-new-line-box">
            <div class="trend-chart-config-new-line-item">
              <div class="trend-chart-config-new-line-item-label">{{ 'Line Width' }}</div>
              <div class="trend-chart-config-new-line-item-form">
                <a-input-number
                  v-model:value="data.lineWidth"
                  :min="1"
                  :max="10"
                  style="width: 200px"
                  @change="lineWidthChange"
                ></a-input-number>
              </div>
            </div>
            <div class="trend-chart-config-new-line-item">
              <div class="trend-chart-config-new-line-item-label">{{ 'X&Y Coordinate fonts' }}</div>
              <div class="trend-chart-config-new-line-item-form">
                <a-input-number
                  v-model:value="data.coordinateFontSize"
                  :min="12"
                  :max="20"
                  style="width: 200px"
                  @change="fontSizeChange"
                ></a-input-number>
              </div>
            </div>
            <div class="trend-chart-config-new-line-item">
              <div class="trend-chart-config-new-line-item-label">{{ 'Y-Axis Max Ticks Limit' }}</div>
              <div class="trend-chart-config-new-line-item-form">
                <a-input-number
                  v-model:value="data.yAxisMaxTicksLimit"
                  :min="3"
                  :max="20"
                  style="width: 200px"
                  @change="fontSizeChange"
                ></a-input-number>
              </div>
            </div>
          </div>
          <div class="trend-chart-config-new-btn-box">
            <a-button :type="'primary'" @click="apply">{{ t('eesCharts.Apply') }}</a-button>
            <a-button style="margin-left: 10px" @click="onClose">{{
              t('eesCharts.Cancel')
            }}</a-button>
          </div>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<style lang="less">
.trend-chart-config-new-popover {
  .trend-chart-config-new-legend-box {
    .trend-chart-config-new-legend-form {
      display: flex;
      flex-wrap: wrap;
      &-item {
        width: 200px;
        margin-right: 10px;
        margin-top: 10px;
      }
      &-item:nth-child(-n + 3) {
        margin-top: 0;
      }
    }
  }
  .trend-chart-config-new-line-box {
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    .trend-chart-config-new-line-item {
      width: 200px;
      margin-right: 10px;
      margin-top: 10;
    }
    .trend-chart-config-new-line-item:nth-child(-n + 3) {
      margin-top: 0;
    }
  }
  .trend-chart-config-new-btn-box {
    text-align: right;
  }
}
</style>
