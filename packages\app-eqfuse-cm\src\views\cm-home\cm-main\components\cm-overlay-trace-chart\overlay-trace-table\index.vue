<script lang="ts" setup>
import { nextTick, ref, shallowRef, watch } from 'vue';
import { getOptions } from './config';
import type { LineItem } from '@/views/cm-home/cm-main/components/cm-result-chart/components/custom-legend/interface';
import { useCustomLegendStore } from '@futurefab/ui-sdk-stores';

const customLegendStore = useCustomLegendStore();
const props = withDefaults(
  defineProps<{
    isLoop: boolean;
    parameterAndColor: any[];
    dataSetList: any[];
  }>(),
  {
    isLoop: false,
    parameterAndColor: () => [],
    dataSetList: () => []
  }
);
const xGrid = ref();
const model = defineModel<any>('selectWaferData');
const chamberValue = defineModel<string[]>('groupId');
const chamberList = ref<{ label: string; value: string }[]>([]);
const tableData = ref<LineItem[]>([]);
const checkCount = ref(0);
const beforeCheckRows = shallowRef<LineItem[]>([]);
function selectedFiftyRows() {
  xGrid.value.setCheckboxRow(tableData.value, true);
  beforeCheckRows.value = tableData.value;
  model.value = beforeCheckRows.value;
  checkCount.value = tableData.value.length;
}
const option = ref();
const chamberChange = (val: string[]) => {
  const tempArr: any = [];
  const { lineItems } = customLegendStore.customLegendData;
  lineItems.forEach((item: any) => {
    if (val.includes(String(item.dataSet))) tempArr.push(item);
  });
  // 通过contextKey来去重
  const uniqueLineItems = tempArr.filter((item: any, index: number, self: any) =>
    index === self.findIndex((t: any) => t.contextKey === item.contextKey)
  );
  tableData.value = uniqueLineItems;
  // check前50行
  nextTick(() => {
    selectedFiftyRows();
  });
};
const handleCheckData = () => {
  const checkRecords = xGrid.value.getCheckboxRecords();
  beforeCheckRows.value = [...checkRecords];
  model.value = [...checkRecords];
  checkCount.value = checkRecords.length;
};
const tableEvent = {
  checkboxAll: (val: any) => {
    handleCheckData();
  },
  checkboxChange: (val: any) => {
    handleCheckData();
    // 以前需要控制最多只能选择50条数据
    // if (checkRecords.length > 50) {
    //     xGrid.value.setCheckboxRow(checkRecords, false);
    //     xGrid.value.setCheckboxRow(beforeCheckRows.value, true);
    //     showWarning(t('common.tip.maxRow', { num: 50 }));
    //     model.value = [...beforeCheckRows.value];
    // } else {
    //     beforeCheckRows.value = [...checkRecords];
    //     model.value = [...checkRecords];
    //     checkCount.value = checkRecords.length;
    // }
  }
};
const cellClick = (rowInfo: any) => {
  const { row, column } = rowInfo;
  if (column?.type !== 'checkbox') {
    xGrid.value.toggleCheckboxRow(row);
    handleCheckData();
  }
};
watch(
  [() => customLegendStore.customLegendData, () => props.isLoop, () => props.dataSetList],
  ([newCustomLegendData]) => {
    option.value = getOptions(props.isLoop);
    chamberList.value = props.dataSetList?.map((item) => ({
      label: item.key,
      value: String(item.id)
    }));
    // 获取所有chamber的ID
    const allChamberIds = chamberList.value.map((item) => item.value);
    // 只有在chamberValue为空时才设置初始值
    if (!chamberValue.value || chamberValue.value.length === 0) {
      chamberValue.value = allChamberIds;
    }
   
    // 通过contextKey来去重
    const uniqueLineItems = newCustomLegendData.lineItems.filter((item: any, index: number, self: any) =>
      index === self.findIndex((t: any) => t.contextKey === item.contextKey)
    );
    tableData.value = uniqueLineItems;
    
    // check前50行
    setTimeout(() => {
      selectedFiftyRows();
    });
  },
  { immediate: true }
);
</script>
<template>
  <div class="cm-overlay-trace-grid">
    <div class="cm-overlay-trace-grid-header">
      <div class="cm-overlay-trace-grid-header-select">
        <span class="title"> {{ $t('cm.label.dataSet') }} </span>
        <a-select
          v-model:value="chamberValue"
          mode="multiple"
          :options="chamberList"
          class="inline-select"
          :disabled="parameterAndColor.length > 0"
          :show-arrow="true"
          :max-tag-count="1"
          @change="chamberChange"
        ></a-select>
      </div>
      <div class="cm-overlay-trace-grid-header-count">
        {{ $t('cm.label.selected') }} {{ checkCount }} / {{ tableData.length }}
      </div>
    </div>
    <div class="cm-overlay-trace-grid-content">
      <vxe-grid
        id="cm-overlay-trace-grid"
        ref="xGrid"
        class="cm-overlay-trace-grid-content-table"
        v-bind="option"
        :data="tableData"
        v-on="tableEvent"
        @cell-click="cellClick"
      >
        <template #checkAllHead>
          <div></div>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.cm-overlay-trace-grid {
  height: 100%;
  width: 100%;
  display: flex;
  flex-shrink: 0;
  background-color: @arrow-bg-color;
  flex-direction: column;
  overflow: hidden;
  &-header {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    align-items: center;
    justify-content: space-between;
    align-items: center;
    color: @on-primary-color-1;
    flex-shrink: 0;
    margin-bottom: 10px;
    &-select {
      display: flex;
      flex: 1;
      align-items: center;
      .title {
        margin: 0 10px 0 0;
      }
      .inline-select {
        display: inline-flex;
        height: 32px;
        flex: 1;
        :deep(.ant-select-selector) {
          display: inline-flex;
          flex: 1;
        }
      }
    }
    &-count {
      margin-left: 10px;
      color: @text-sub-color;
    }
  }
  &-content {
    flex-grow: 1;
    overflow: hidden;
    &-table {
      height: 100%;
    }
  }
}
</style>
