<script setup lang="ts">
import { ref, reactive, onMounted, provide, computed, onUnmounted, watch, shallowRef } from 'vue';
import { useRoute } from 'vue-router';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { CmAnalysisOptions } from '@/model/cm-analysis-options';
import { ReferenceType } from '@/model/reference-type';
import {
  getLastAnalysis,
  clearResultAnalysis,
  getDynamicReferenceService,
  notifyBackendStartScreenshot,
  generateCmResultPPTService,
  setPPTProgress,
  getContextList,
  getCustomLegendData
} from '@futurefab/ui-sdk-api';
import {
  setConfigTable,
  setAnalysisResult,
  cancelRunAnalysis
} from '@/views/cm-home/cm-main/config';
import { cmMainInfo as buttonIds } from '@/log-config';
import CmResultChart from '@/views/cm-home/cm-main/components/cm-result-chart/index.vue';
import { showError, showWarning, successInfo } from '@futurefab/ui-sdk-ees-basic';
import {
  downloadImage,
  waitForElementStable,
  captureChartElement as captureChartElementUtil,
  captureTraceChart as captureTraceChartUtil
} from '@/utils/screenshot';
import { useMetrology } from '../cm-home/cm-main/useMetrology';
import { useCustomLegendStore } from '@futurefab/ui-sdk-stores';

const route = useRoute();

// 初始化数据
let groupConfigs: GroupConfigVO[] = [];
let referenceTypes: ReferenceType[] = [];
let commonGroupConfigVO: GroupConfigVO = new GroupConfigVO();
let analysisOptions: CmAnalysisOptions = new CmAnalysisOptions();

// 初始化配置信息
const initDInfo = {
  iconFontSize: '18px',
  btnSize: 'small',
  showDBImport: false,
  showFileImport: false,
  showContextFilter: false,
  showMenu: false,
  showExport: false,
  analysisOptions,
  showCharts: false,
  columnsOutputNumber: 4,
  parameterOutputNumber: 20,
  chartWidth: 300,
  groupConfigs,
  selectedReferenceType: 'static',
  dynamicReferenceAnalysisId: undefined,
  splitByTool: false,
  splitByChamber: false,
  widgetId: 1,
  manualAnalysis: true,
  datasetConfigurationId: null,
  contextFilter: null,
  isRegexSearch: false,
  showLoading: false,
  loadingTitle: '',
  analysisId: null,
  resultIdMap: [],
  resultRows: [],
  resultGroups: [],
  useDataNormalization: false,
  showAnalysisSpinner(msg: string, showLoading?: boolean, shouldCancel?: boolean) {
    d.loadingTitle = msg;
    if (showLoading != undefined) {
      d.showLoading = showLoading;
    }
    if (shouldCancel && showLoading === false) {
      cancelRunAnalysis();
    }
  }
};

// 主要数据对象
let d = reactive({
  ...initDInfo,
  selectedHeaderColumns: [],
  tableColumns: [],
  tableData: [] as any[],
  commonGroupConfigVO,
  userConfig: {},
  selectedCMCategories: [],
  selectedCMParameterGroups: [],
  selectedCMParameters: [],
  referenceTypes,
  trimRuleList: [],
  waferData: [] as any[],
  // 是否展示未选中的数据
  showNoAnalysisParam: false,
  // 是否使用数据标准化
  useDataNormalization: false,
  // 零飘
  zeroTicket: false
});

// 组件引用
const cmResultChart = ref();
const websocketParams = ref<any>({});
const unMatchStepInfo = ref({});
const stepInfo = ref<any[]>([]);
const isHistory = ref<boolean>(false);

// 截图相关
let sseId = '';
let exportConfig = reactive<any>({});
let screentshotTotalCount = ref(0);
let isScreenShotCancelled = true;
let allScreenshotData: Array<any> = [];

// 截图模式管理
let traceChartCaptureMode = ref(false);
let highlightStep = ref(false);
let isScreenshotMode = ref(true);

// 提供截图模式给子组件
provide('traceChartCaptureMode', traceChartCaptureMode);
provide('highlightStep', highlightStep);
provide('isScreenshotMode', isScreenshotMode);

// 截图进度
let exportProgress = reactive({
  time: new Date(),
  log: '',
  status: 'DOING',
  data: {
    currentScreentshotIndex: 0,
    screentshotTotalCount: 0,
    isExportFinish: false
  }
});

// metrology
const { hasMetrology, meteroloyWaferData } = useMetrology(isHistory, d);

// custom legend store
const customLegendStore = useCustomLegendStore();

const modelInit = async () => {
  const params = {
    ...websocketParams.value
  };
  if (params?.requestId && params?.requestDt) {
    const paramList = await getContextList({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-context-list'
      }
    });

    if (paramList.status === 'SUCCESS') {
      d.waferData = paramList.data;
    }
  }
};

watch(
  () => websocketParams.value.requestId,
  () => {
    modelInit();
  }
);

// 根据filterParameter获取选中的参数名称
const getSelectedItemNames = (filterParameter: any): string[] => {
  const selectedItemNames: string[] = [];

  if (filterParameter.checkedItems) {
    Object.values(filterParameter.checkedItems).forEach((group: any) => {
      Object.entries(group).forEach(([name, isChecked]) => {
        if (isChecked) {
          // 移除前缀，获取实际的参数名称
          const parameterName = name.split('-').slice(1).join('-');
          selectedItemNames.push(parameterName);
        }
      });
    });
  }

  return selectedItemNames;
};

// 计算当前选中的参数信息
const selectedParametersInfo = computed(() => {
  if (!exportConfig.filterParameter) {
    return '未配置截图参数';
  }

  const selectedItemNames = getSelectedItemNames(exportConfig.filterParameter);
  if (selectedItemNames.length === 0) {
    return '未选择任何参数';
  }

  return `已选择 ${selectedItemNames.length} 个参数: ${selectedItemNames.join(', ')}`;
});

// 截图函数实现
const captureTraceChart = async () => {
  if (isScreenShotCancelled) return null;

  try {
    // 等待轨迹图元素稳定
    await waitForElementStable('#cm-screenshot-chart .cm-chart-copy', 1500);

    const traceChartImg = await captureTraceChartUtil('#cm-screenshot-chart .cm-chart-copy');

    if (traceChartImg) {
      return {
        sort: 2.0,
        imgType: 'traceChart',
        base64ImgData: traceChartImg
      };
    } else {
      console.warn('轨迹图截图失败');
      return null;
    }
  } catch (error) {
    exportProgress.log = `轨迹图截图失败: ${JSON.stringify(error)}`;
    exportProgress.data.isExportFinish = false;
    setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
    console.error('轨迹图截图失败:', error);
    return null;
  }
};

const captureChartElement = async (selector: string, step: string, chartType: string) => {
  if (isScreenShotCancelled) return null;

  try {
    // 查找对应的图表元素
    const chartElement = document.querySelector(selector) as HTMLElement;

    if (!chartElement) {
      console.warn(`未找到元素: ${selector}`);
      return null;
    }

    // 等待图表元素稳定
    await waitForElementStable(selector, 1000);

    const result = await captureChartElementUtil(selector, step, chartType);

    return result;
  } catch (error) {
    console.error(`步骤${step}截图失败:`, error);
    exportProgress.log = `步骤${step}截图失败: ${JSON.stringify(error)}`;
    exportProgress.data.isExportFinish = false;
    setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
    return null;
  }
};

// 执行截图逻辑
const performScreenshotWithSelectedRows = async (selectedRows: any[]) => {
  // 清空现有数据
  allScreenshotData = [];
  exportProgress.log = `开始截图: 数据总数:${selectedRows.length}个`;
  setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
  try {
    isScreenShotCancelled = false;
    screentshotTotalCount.value = selectedRows.length;
    d.showLoading = true;

    // 等待图表完全渲染完成
    d.loadingTitle = '等待图表完全渲染完成...';

    // 等待轨迹图元素稳定
    await waitForElementStable('#cm-screenshot-chart .cm-chart-copy', 1000);

    exportProgress.log = '图表渲染完成，开始截图...';
    d.loadingTitle = '图表渲染完成，开始截图...';

    const { cmAnalysisResultList } = exportConfig.exportFilter;

    highlightStep.value = cmAnalysisResultList.includes('highlightStep');

    for (let i = 0; i < selectedRows.length; i++) {
      exportProgress.data.currentScreentshotIndex = i + 1;
      exportProgress.log = `开始截图: 总数据量:${selectedRows.length}，当前第${i + 1}个参数`;
      setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
      if (isScreenShotCancelled) {
        break;
      }

      const row = selectedRows[i];
      d.loadingTitle = `正在截图中：${row.parameterName}...(${i + 1}/${
        screentshotTotalCount.value
      })`;

      // Step1: 初始化当前行数据结构
      const rowData: {
        paramName: string;
        items: Array<{
          sort: number;
          imgType: string;
          base64ImgData: string;
          step: string;
        }>;
        traceChartImg: string;
      } = {
        paramName: row.parameterName,
        items: [],
        traceChartImg: ''
      };

      // Step1.5: 切换到当前行的参数数据，确保图表显示正确的数据
      if (websocketParams.value && websocketParams.value.paramAliasList) {
        // 更新 websocketParams 中的参数列表为当前行
        const updatedParams = {
          ...websocketParams.value,
          paramAliasList: [row.parameterName]
        };
        websocketParams.value = updatedParams;

        // 通知图表组件切换到当前参数
        await cmResultChart.value.handleSendSocket(updatedParams);

        // 模拟 cm-data-set-table 中点击行的行为
        const currentRow = d.tableData.find((item) => item.parameterName === row.parameterName);
        if (currentRow) {
          // 1. 设置 showCharts 为 true
          d.showCharts = true;

          // 2. 更新 unMatchStepInfo
          unMatchStepInfo.value = currentRow;

          // 3. 检查参数是否变化，如果变化则调用 handleSendSocket
          if (updatedParams?.paramAliasList !== websocketParams.value?.paramAliasList) {
            websocketParams.value = { ...websocketParams.value, ...updatedParams };
            const paramAliasList = websocketParams.value?.paramAliasList?.filter(
              (item: any) => !!item
            );
            if (
              currentRow?.parameterName !== 'Ungrouped' &&
              paramAliasList &&
              paramAliasList.length > 0
            ) {
              await cmResultChart.value.handleSendSocket(websocketParams.value);
            }
          }

          // 等待图表数据更新完成
          await new Promise((resolve) => setTimeout(resolve, 800));
        }
      }

      // Step2: 截图tracechart
      exportProgress.data.currentScreentshotIndex = i + 1;
      exportProgress.log = `正在截图: 总数据量:${selectedRows.length}，当前第${i + 1}个，第${i + 1}个: tracechart截图开始`;
      setPPTProgress({ bodyParams: { sseId, info: exportProgress } });

      const traceResult = await captureTraceChart();
      if (traceResult) {
        rowData.traceChartImg = traceResult.base64ImgData;
        // 立即下载trace chart
        // downloadImage(
        //   traceResult.base64ImgData,
        //   row.parameterName,
        //   'trace',
        //   'traceChart'
        // );
        exportProgress.log = `第${i + 1}行: trace chart下载完成`;
      }

      if (
        Array.isArray(cmAnalysisResultList) &&
        !cmAnalysisResultList.includes('boxplot') &&
        !cmAnalysisResultList.includes('summary') &&
        !cmAnalysisResultList.includes('metrology')
      ) {
        allScreenshotData.push(rowData);
        continue;
      }

      // Step3: 获取当前行的所有 红色 step 并循环截图
      await new Promise((resolve) => setTimeout(resolve, 500));

      const unmatchedStepList = cmResultChart.value.getUnmatchedStepList();
      if (unmatchedStepList && unmatchedStepList.length > 0) {
        for (const unmatchedStep of unmatchedStepList) {
          await cmResultChart.value.switchStep(unmatchedStep);

          // 截图当前step下的boxplot
          if (cmAnalysisResultList.includes('boxplot')) {
            exportProgress.data.currentScreentshotIndex = i + 1;
            exportProgress.log = `正在截图: 总数据量:${selectedRows.length}，当前第${i + 1}个参数 Step${unmatchedStep} boxplotResult 开始截图`;
            setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
            // 等待 boxplot 图表渲染完成
            await waitForElementStable('#cm-boxplot-chart-container', 800);

            const boxplotResult = await captureChartElement(
              '#cm-boxplot-chart-container',
              unmatchedStep,
              'boxplot'
            );

            if (boxplotResult) {
              rowData.items.push(boxplotResult);
            }
          }

          // 截图当前step下的summary
          if (cmAnalysisResultList.includes('summary')) {
            exportProgress.data.currentScreentshotIndex = i + 1;
            exportProgress.log = `正在截图: 总数据量:${selectedRows.length}，当前第${i + 1}个参数 Step${unmatchedStep} summarychart 开始截图`;
            setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
            // 等待 summary 图表渲染完成
            await waitForElementStable('#cm-summary-chart', 500);

            const summaryResult = await captureChartElement(
              '#cm-summary-chart',
              unmatchedStep,
              'summary'
            );

            if (summaryResult) {
              rowData.items.push(summaryResult);
            }
          }

          // 截图当前step下的metrology
          if (cmAnalysisResultList.includes('metrology')) {
            // 获取所有 metrologyOptions
            const metrologyOptions = cmResultChart.value.getMetrologyOptions();

            if (metrologyOptions && metrologyOptions.length > 0) {
              // 循环遍历每个 metrologyOption
              for (const metrologyOption of metrologyOptions) {
                exportProgress.data.currentScreentshotIndex = i + 1;
                exportProgress.log = `开始循环: 总数据量:${selectedRows.length}，当前，第${i + 1}行 Step${unmatchedStep} metrology(${metrologyOption.label}) 开始截图`;
                setPPTProgress({ bodyParams: { sseId, info: exportProgress } });

                // 切换 metrology 参数
                await cmResultChart.value.switchMetrologyParam(metrologyOption.value);

                // 等待 metrology 图表渲染完成
                await waitForElementStable('#cm-metrology-chart-container', 500);
                const metrologyResult = await captureChartElement(
                  '#cm-metrology-chart-container',
                  `${unmatchedStep}_${metrologyOption.label}`,
                  'metrology'
                );

                if (metrologyResult) {
                  rowData.items.push(metrologyResult);
                }
              }
            }
          }
        }
      }
      allScreenshotData.push(rowData);
    }

    // 所有行处理完成后
    if (allScreenshotData.length > 0) {
      successInfo(`截图完成，共${allScreenshotData.length}个参数。`);

      exportProgress.log = `截图完成，共${allScreenshotData.length}条数据，正在生成文件中...`;
      setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
      try {
        const requestData = {
          sseId,
          requestId: websocketParams.value?.requestId,
          layoutSetting: exportConfig.exportFilter.layoutSetting,
          paramItems: allScreenshotData,
          format: exportConfig.exportFilter.format
        };

        generateCmResultPPTService({ bodyParams: requestData }).then((res) => {
          if (res.status === 'FAIL') {
            exportProgress.data.isExportFinish = false;
            exportProgress.log = `生成文件失败:${res.msg}`;
            exportProgress.status = 'CLOSE';
            setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
          } else {
            exportProgress.data.isExportFinish = true;
            successInfo('文件生成完成！');
          }
          sessionStorage.removeItem('cm_sseId');

          window.close();
        });
      } catch (error) {
        exportProgress.data.isExportFinish = false;
        sessionStorage.removeItem('cm_sseId');
        console.error('生成文件失败:', error);
        showError('生成文件失败，请重试');
      }
    } else {
      showWarning('未收集到任何截图数据');
    }
  } finally {
    d.showLoading = false;
    d.loadingTitle = '';
    //sessionStorage.removeItem('cm_sseId');
  }
};

const allSteps = shallowRef([]);
// 获取最后分析结果
const getLastAnalysisResult = async (historyInfo?: any) => {
  d.showAnalysisSpinner('Preparing data...', true);
  let params = {};

  if (historyInfo) {
    params = historyInfo;
  } else {
    // 从URL参数获取数据
    const urlParams = new URLSearchParams(window.location.search);
    const requestDt = urlParams.get('requestDt');
    const requestId = urlParams.get('requestId');
    const urlSseId = urlParams.get('sseId');

    if (urlSseId) {
      sseId = urlSseId;
    }

    if (requestDt && requestId) {
      params = {
        requestDt,
        requestId
      };
    }
  }

  try {
    const lastAnalysis = await getLastAnalysis({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-last-analysis'
      }
    });

    if (lastAnalysis.status === 'SUCCESS') {
      isHistory.value = true;
      const { request, result } = lastAnalysis.data;

      if (!request || !result) {
        d.showAnalysisSpinner('Preparing data...', false);
        return;
      }

      try {
        // 设置allSteps数据
        if (request.allSteps) {
          allSteps.value = request.allSteps;
        }
        // 构建入参
        await setConfigTable(request, d, renderNewGroupConfigs);
        // 构建分析结果
        await setAnalysisResult(result, d, websocketParams, sendWebSocket, toggleCharts, request);

        const customLegendRes = await getCustomLegendData({
          bodyParams: {
            requestId: request.requestId,
            requestDt: request.requestDt
          }
        });
        // 存储customLegendData
        if (customLegendRes.status === 'SUCCESS' && customLegendRes.data?.length > 0) {
          customLegendStore.setCustomLegendData({
            legendByCustom: customLegendRes?.data?.[0]?.infoJson?.legendByCustom,
            legendBy: customLegendRes?.data?.[0]?.infoJson?.legendBy,
            lineItems: customLegendRes?.data?.map((item: any) => ({
              ...item.infoJson.lineItems?.[0],
              ctxExtendedId: item.ctxExtendedId
            }))
          });
        }

        d.showAnalysisSpinner('Preparing data...', true);
        setTimeout(() => {
          handleScreenShot();
        }, 1000);
      } catch (err) {
        console.error('构建入参失败:', err);
        exportProgress.log = `构建入参失败:${JSON.stringify(err)}`;
        exportProgress.data.isExportFinish = false;
        setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
      }
    } else {
      d.showAnalysisSpinner('Preparing data...', false);
      console.log('获取分析结果失败:', lastAnalysis);
    }
  } catch (error) {
    console.error('getLastAnalysis调用失败:', error);
    d.showAnalysisSpinner('Preparing data...', false);
    exportProgress.log = `getLastAnalysis调用失败: ${JSON.stringify(error)}`;
    exportProgress.data.isExportFinish = false;
    setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
  }
};

// 渲染新的分组配置
const renderNewGroupConfigs = (commonGroupConfigVO: any) => {
  d.commonGroupConfigVO = commonGroupConfigVO;
  d.columnsOutputNumber = d.commonGroupConfigVO?.groupConfigs.length || 0;
  renderTable();
};

// 渲染表格
const renderTable = () => {
  let tb = d.commonGroupConfigVO.createGroupConfigTableDataAndColumn();
  d.tableData = tb.tableData as never;
  d.tableColumns = tb.tableColumns as never;
};

// 切换图表显示
const toggleCharts = (b: boolean) => {
  d.showCharts = b;
};

// 发送WebSocket
const sendWebSocket = (params: any, row?: any) => {
  d.showCharts = true;
  if (row) {
    unMatchStepInfo.value = row;
    if (params?.paramAliasList !== websocketParams.value?.paramAliasList) {
      websocketParams.value = { ...websocketParams.value, ...params };
      const paramAliasList = websocketParams.value?.paramAliasList?.filter((item: any) => !!item);
      if (row?.parameterName !== 'Ungrouped' && paramAliasList && paramAliasList.length > 0) {
        cmResultChart.value.handleSendSocket(websocketParams.value);
      }
    }
  } else {
    unMatchStepInfo.value = {};
    websocketParams.value = { ...websocketParams.value, ...params };
    const paramAliasList = websocketParams.value?.paramAliasList?.filter((item: any) => !!item);
    if (paramAliasList && paramAliasList.length > 0) {
      cmResultChart.value.handleSendSocket(websocketParams.value);
    }
  }
};

// 发送覆盖数据
const sendOverlayData = (data: any) => {
  console.log('### sendOverlayData:', data);
};
// 根据配置执行截图
const executeScreenshotByConfig = async () => {
  if (!exportConfig.filterParameter) {
    showWarning('配置中缺少filterParameter，无法确定要截图的参数');
    return;
  }

  // 获取选中的参数名称
  const selectedItemNames = getSelectedItemNames(exportConfig.filterParameter);

  if (selectedItemNames.length === 0) {
    showWarning('没有选择任何参数进行截图');
    return;
  }
  if (d.tableData && d.tableData.length > 0) {
    const selectedRows = d.tableData.filter((item) => {
      return selectedItemNames.includes(item.parameterName);
    });

    if (selectedRows.length > 0) {
      await performScreenshotWithSelectedRows(selectedRows);
    } else {
      showWarning('没有找到匹配的参数行数据');
    }
  } else {
    showWarning('没有表格数据可供截图');
  }
};

// 处理截图
const handleScreenShot = async () => {
  d.showAnalysisSpinner('准备截图，请稍候...', true);

  const { query } = route;
  if (query.isScreenshot && query.config) {
    let configData: any;
    try {
      configData = typeof query.config === 'string' ? JSON.parse(query.config) : query.config;

      // 保存配置
      exportConfig = configData;

      // 处理legendType设置
      if (exportConfig.exportFilter?.legendType) {
        const legendType = exportConfig.exportFilter.legendType;

        if (legendType === 'custom') {
          // 使用cm-main中传递过来的custom legend设置
          if (exportConfig.customLegendData) {
            customLegendStore.setCustomLegendData(exportConfig.customLegendData);
          }
        } else {
          // 设置为默认图例模式（按Group）
          customLegendStore.setCustomLegendData({
            legendByCustom: false,
            legendBy: '',
            lineItems: []
          });
        }

        // 等待图例状态更新
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      // 根据配置执行截图
      await executeScreenshotByConfig();
    } catch (error) {
      console.error('解析config参数失败:', error);
      showError('配置参数解析失败');
      exportProgress.log = `配置参数解析失败: ${JSON.stringify(error)}`;
      exportProgress.data.isExportFinish = false;
      setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
      return;
    }
  }
};

const changeColumnNameColor = (event: any) => {
  [...(d.tableColumns || []), ...(d.commonGroupConfigVO?.groupConfigs || [])].forEach(
    (item: any) => {
      if (item.id && event[item.id + '']) {
        const temp = event[item.id + ''];
        item.key = temp.groupNameAlias;
        item.color = temp.groupColor;
        item.chartLegendColor = temp.groupColor;
      }
    }
  );
  if (d.tableColumns) {
    d.tableColumns = [...d.tableColumns];
  }
};

onMounted(async () => {
  try {
    await getLastAnalysisResult();
  } catch (err) {
    console.error('获取最后分析结果失败:', err);
    exportProgress.log = `获取最后分析结果失败:${err}`;
    exportProgress.data.isExportFinish = false;
    setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
  }
  window.addEventListener('beforeunload', handleBeforeUnload);
});

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

const handleBeforeUnload = async (e: any) => {
  exportProgress.log = '页面关闭';
  exportProgress.status = 'CLOSE';
  setPPTProgress({ bodyParams: { sseId, info: exportProgress } });
};
</script>

<template>
  <div
    class="cm-screen-container"
    v-isLoading="{
      isShow: d.showLoading,
      hasButton: false,
      title: d.loadingTitle
    }"
  >
    <div class="content">
      <div class="chart-section">
        <cm-result-chart
          ref="cmResultChart"
          id="cm-screenshot-chart"
          v-model:step-info="stepInfo"
          :show-charts="d.showCharts"
          :allSteps="allSteps"
          :box-detail="{
            commonGroupConfigVO: d.commonGroupConfigVO,
            useDataNormalization: d.useDataNormalization,
            traceMatchingType: d.analysisOptions.traceMatchingType,
            tableData: d.tableData
          }"
          :tableColumns="d.tableColumns"
          :un-match-step-info="unMatchStepInfo"
          :waferData="meteroloyWaferData"
          :hasMetrology="hasMetrology"
          :split-legend="false"
          @send-overlay-data="sendOverlayData"
          @changeColumn="changeColumnNameColor"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.cm-screen-container {
  padding: 20px;
  // height: 100vh;
  display: flex;
  flex-direction: column;

  .header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 15px 0;
      color: #333;
    }

    .status-info {
      display: flex;
      gap: 20px;

      p {
        margin: 0;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
        font-size: 14px;
        color: #666;
      }
    }

    .action-buttons {
      margin-top: 15px;

      .screenshot-btn {
        padding: 8px 16px;
        background-color: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;

        &:hover:not(:disabled) {
          background-color: #40a9ff;
        }

        &:disabled {
          background-color: #d9d9d9;
          cursor: not-allowed;
          color: #999;
        }
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .visibility-notice {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 20px;

      .notice-content {
        text-align: center;

        h4 {
          margin: 0 0 8px 0;
          color: #1890ff;
          font-size: 16px;
        }

        p {
          margin: 4px 0;
          color: #666;
          font-size: 14px;
        }
      }
    }

    .chart-section {
      flex: 1;
      border-radius: 4px;
      overflow: hidden;
      min-height: 400px;

      :deep(.splitpanes__pane) {
        width: 1000px !important;
        height: 600px !important;
      }
      :deep(.splitpanes) {
        width: 1000px !important;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
