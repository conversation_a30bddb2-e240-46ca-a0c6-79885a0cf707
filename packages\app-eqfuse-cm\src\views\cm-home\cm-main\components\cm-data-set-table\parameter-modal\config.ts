import { VXETable } from '@futurefab/vxe-table';
import { cloneDeep } from 'lodash-es';
import { ResultGroupInfo } from '@/model/ResultGroupInfo';
import type {
  GroupConfig,
  MatchingInfo
} from '@/views/cm-home/cm-main/components/cm-data-set-table/interface';

export const getCategoryTableOptions = () => {
  const options = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'checkbox'
      },
      {
        field: 'category',
        title: 'cm.field.category',
        minWidth: 120,
        sortable: false,
        filters: false,
        slots: {
          default: 'categorySlot'
        }
      }
    ]
  });
  options.columns.shift();
  return options;
};
export const getGroupTableOptions = () => {
  const options = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'checkbox'
      },
      {
        field: 'parameterName',
        title: 'cm.field.parameterGroup',
        minWidth: 120,
        sortable: true,
        filters: false
      }
    ]
  });
  options.columns.shift();
  return options;
};
export const getParameterTableOptions = () => {
  const options = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        type: 'checkbox'
      },
      {
        field: 'parameterName',
        title: 'cm.field.parameter',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      }
    ]
  });
  options.columns.shift();
  return options;
};
// 计算分组后的match分数值
export const getMatchingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let matchingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      matchingCount += item.matchingCount;
    });
  return totalCount < 1 ? '0' : ((matchingCount / totalCount) * 100).toFixed(2);
};
export const getUnmatchingWarningRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let unmatchingCount = 0;
  let warningCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      unmatchingCount += item.unmatchingCount;
      warningCount += item.warningCount;
    });
  return totalCount < 1 ? '0' : ((unmatchingCount + warningCount / totalCount) * 100).toFixed(2);
};
export const getUnmatchingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let unmatchingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      unmatchingCount += item.unmatchingCount;
    });
  return totalCount < 1 ? '0' : ((unmatchingCount / totalCount) * 100).toFixed(2);
};
export const getWarningRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let warningCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      warningCount += item.warningCount;
    });
  return totalCount < 1 ? '0' : ((warningCount / totalCount) * 100).toFixed(2);
};
export const getMissingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let missingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      missingCount += item.missingCount;
    });
  return totalCount < 1 ? '0' : ((missingCount / totalCount) * 100).toFixed(2);
};
// 数据筛选-数据过滤后进行组装
const filterRecipeText = (inputString: string, filterArray: string[]): string => {
  if (!inputString) return '';
  const stepsPart = inputString?.replace('Rcp. Step ', '');
  const stepsArray = stepsPart.split(',').map((step) => step.trim());
  const filteredSteps = stepsArray.filter((step) => filterArray.includes(step));
  if (filteredSteps.length === 0) {
    return '';
  }
  return `Rcp. Step ${filteredSteps.join(', ')}`;
};
// 更新最不匹配的前五项文案颜色
const setUnMatchTop5TextFlat = (newValue: any, newColumns: any, key: number) => {
  const column = newColumns.find((column: any) => column?.id === key);
  if (column?.resultGroupInfo && column?.resultGroupInfo?.unMatchingTop5) {
    const unMatchingTop5 = column?.resultGroupInfo?.unMatchingTop5?.map(
      (item: { parameterName: string }) => item.parameterName
    );
    if (unMatchingTop5.length > 0 && unMatchingTop5.includes(newValue.parameterName)) {
      newValue.isUnMatchingTop5 = true;
    }
  }
};
const filterSteps = (deepTwo: any, stepCheckId: string[], newColumns: any, groupId?: number) => {
  if (groupId !== deepTwo?.groupId) return false;
  const newDeepTwo = deepTwo;
  const newAllColInfo = new Map();
  deepTwo.allColInfo.forEach((value: any, key: number) => {
    // 处理每一列从第3行到第n行的数据
    const newValue = cloneDeep(value);
    if (stepCheckId && stepCheckId.length > 0) {
      const recipeText = filterRecipeText(value.recipeText, stepCheckId);
      newValue.recipeStepSummaryItems = value.recipeStepSummaryItems.filter((item: any) =>
        stepCheckId.includes(item.recipeStepId)
      );
      newValue.unmatchingRecipeSteps = value.unmatchingRecipeSteps.filter((item: any) =>
        stepCheckId.includes(item.recipeStepId)
      );
      newValue.warningRecipeSteps = value.warningRecipeSteps.filter((item: any) =>
        stepCheckId.includes(item.recipeStepId)
      );
      newValue.recipeText = recipeText;
    }
    setUnMatchTop5TextFlat(newValue, newColumns, key);
    newAllColInfo.set(key, newValue);
  });
  newDeepTwo.allColInfo = newAllColInfo;
  return newDeepTwo;
};
export const filterOriginData = (
  originData: any,
  groupCheckData: any,
  parameter: any,
  stepCheck: any,
  columnData: any
) => {
  const newTableData: any = originData.slice(0, 2);
  let stepsTemp: any = {};
  const groupName = groupCheckData?.map((item: any) => item.parameterName);
  const parameterName = parameter?.map((item: any) => item.parameterName);
  const stepCheckId = stepCheck?.map((item: any) => item.recipeStepId);
  // 计算第二行
  const tempOneTwoLines = originData.slice(0, 2);
  const newColumns = computedRow2Data(
    columnData,
    tempOneTwoLines,
    groupName,
    parameterName,
    stepCheckId
  );
  // 计算第三行
  let groupId: any;
  originData.slice(2).forEach((deepOne: any) => {
    if (deepOne?.groupId === null && deepOne?.groupConfigId) {
      if (
        (groupName && groupName.length === 0) ||
        (groupName && groupName.length > 0 && groupName?.includes(deepOne?.parameterName))
      ) {
        groupId = deepOne?.parentGroupId;
        newTableData.push(deepOne);
      }
    } else {
      // 分组内容
      if (
        (parameterName && parameterName.length === 0) ||
        (parameterName &&
          parameterName.length > 0 &&
          parameterName.includes(deepOne?.parameterName))
      ) {
        stepsTemp = filterSteps(cloneDeep(deepOne), stepCheckId, newColumns, groupId);
        // 内容步骤
        if (stepsTemp) newTableData.push(stepsTemp);
      }
    }
  });
  return { newTableData, newColumns };
};
const computeResultGroupNumber = (rg: ResultGroupInfo) => {};
/**
 * 根据分组数据 参数数据 step数据对数据进行过滤
 */
const filterParameterAndStep = (
  results: any[],
  parameterName: string[],
  stepCheckIds: string[]
) => {
  let count = 0;
  return results
    .map((deepOne) => {
      // 存在参数过滤或第一次进来的时候没有选择参数
      if (
        (parameterName && parameterName.length === 0) ||
        (parameterName &&
          parameterName.length > 0 &&
          parameterName.includes(deepOne?.parameterName))
      ) {
        count++;
        deepOne.isUnMatchingTop5 = count <= 5;
        if (stepCheckIds.length > 0) {
          // step过滤
          deepOne.recipeStepSummaryItems = deepOne.recipeStepSummaryItems.filter(
            (deepTwo: { recipeStepId: string }) => stepCheckIds.includes(deepTwo.recipeStepId)
          );
          deepOne.unmatchingRecipeSteps = deepOne.unmatchingRecipeSteps.filter(
            (deepTwo: { recipeStepId: string }) => stepCheckIds.includes(deepTwo.recipeStepId)
          );
          deepOne.warningRecipeSteps = deepOne.warningRecipeSteps.filter(
            (deepTwo: { recipeStepId: string }) => stepCheckIds.includes(deepTwo.recipeStepId)
          );
        }
        return deepOne;
      }
      return null;
    })
    .filter((deepOne) => deepOne !== null);
};
const filterResultGroupInfos = (
  resultGroupInfo: ResultGroupInfo,
  groupName: string[],
  parameterName: string[],
  stepCheckId: string[]
) => {
  // 首先确定是否是当前选中的分组内容
  if (
    (groupName && groupName.length === 0) ||
    (groupName && groupName.length > 0 && groupName?.includes(resultGroupInfo?.parameterName))
  ) {
    // resultGroupInfo.children
    resultGroupInfo.results = filterParameterAndStep(
      resultGroupInfo.results,
      parameterName,
      stepCheckId
    );
    resultGroupInfo.setUnMatchingTop5();
    // 计算match unMatch warn的数量
    computeResultGroupNumber(resultGroupInfo);
    return resultGroupInfo;
  }
  return undefined;
};
// 列排序规则
const sortResultInfo = (resultGroupInfo: ResultGroupInfo[]) => {
  return resultGroupInfo.sort((a: any, b: any) => {
    // 标题排在最前面
    if (Array.isArray(a) || Array.isArray(b)) return Array.isArray(a) ? -1 : 1;
    // 首先按 reference 排序，为 true 的排在前面
    if (a.reference !== b.reference) return a.reference ? -1 : 1;
    // 按 matchingRatio 升序排列
    const aMatchingRatio = Number(getMatchingRatio(a?.resultGroupInfos, a?.id)) ?? Infinity;
    const bMatchingRatio = Number(getMatchingRatio(b?.resultGroupInfos, b?.id)) ?? Infinity;
    if (aMatchingRatio !== bMatchingRatio) return aMatchingRatio - bMatchingRatio;

    // 按 getUnmatchingWarningRatio 降序排列
    const aUnmatchingWarningRatio =
      Number(getUnmatchingWarningRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bUnmatchingWarningRatio =
      Number(getUnmatchingWarningRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aUnmatchingWarningRatio !== bUnmatchingWarningRatio) {
      return bUnmatchingWarningRatio - aUnmatchingWarningRatio;
    }
    // 按 getUnmatchingRatio 降序排列
    const aUnmatchingRatio = Number(getUnmatchingRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bUnmatchingRatio = Number(getUnmatchingRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aUnmatchingRatio !== bUnmatchingRatio) {
      return bUnmatchingRatio - aUnmatchingRatio;
    }
    // 按 getWarningRatio 降序排列
    const aWarningRatio = Number(getWarningRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bWarningRatio = Number(getWarningRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    if (aWarningRatio !== bWarningRatio) {
      return bWarningRatio - aWarningRatio;
    }
    // 按 getMissingRatio 升序排列
    const aMissingRatio = Number(getMissingRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
    const bMissingRatio = Number(getMissingRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
    return aMissingRatio - bMissingRatio;
  });
};
/**
 * 输出一二行数据,并根据match值排序
 * @param columnData 列的数据
 * @param tempOneTwoLines 前两行的数据
 * @param groupName 所选分组信息
 * @param parameterName 所选参数信息
 * @param stepCheckId 所选step信息
 */
export const computedRow2Data = (
  columnData: GroupConfig[],
  tempOneTwoLines: any[],
  groupName: string[],
  parameterName: string[],
  stepCheckId: string[]
): void => {
  const columns = cloneDeep(columnData);
  const matchingInfo: MatchingInfo = {
    unmatchingCountAll: 0,
    warningCountAll: 0,
    matchingCountAll: 0,
    missingCountAll: 0,
    totalCountAll: 0
  };
  // 处理数据
  columns?.forEach((item: GroupConfig) => {
    if (item?.resultGroupInfo) {
      item.resultGroupInfos = item.resultGroupInfos?.filter((groups: any) => {
        if (groupName && groupName.length > 0) return groupName?.includes(groups.parameterName);
        return true;
      });
      const groups = item.resultGroupInfos?.filter((groupOne: any) => {
        if (groupName && groupName.length > 0)
          return item.id === groupOne.id && groupName?.includes(groupOne.parameterName);
        return item.id === groupOne.id;
      });
      groups?.forEach((group: any) => {
        item.resultGroupInfo = filterResultGroupInfos(group, groupName, parameterName, stepCheckId);
        const info: ResultGroupInfo | undefined = item.resultGroupInfo;
        if (info) {
          matchingInfo.unmatchingCountAll += info.unmatchingCount;
          matchingInfo.warningCountAll += info.warningCount;
          matchingInfo.matchingCountAll += info.matchingCount;
          matchingInfo.missingCountAll += info.missingCount;
          matchingInfo.totalCountAll += info.totalCount;
        }
      });
    }
  });
  // 修改match unMatch warn统计值
  tempOneTwoLines[1].matchingInfo = matchingInfo;
  // 根据match值排序
  sortResultInfo(columns);
  tempOneTwoLines[0].configColumns = columns;

  return columns;
};
