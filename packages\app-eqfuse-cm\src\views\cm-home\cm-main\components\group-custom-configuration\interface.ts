export interface GroupLegendConfig {
    groupName: string;
    alias: string;
    defaultColor: string;
    customColor: string;
    id: number;
}

export interface ChartConfig {
    groupLegendConfigs: GroupLegendConfig[];
    legendType: 'byGroup' | 'Customize';
    lineWidth: number;
    coordinateFontSize: number;
}

export interface NewGroupLegendConfig {
    id: number;
    name: string;
    originName: string;
    color: string;
    originColor: string;
}

export interface NewChartConfig {
    groupLegendConfigs: NewGroupLegendConfig[];
    lineWidth: number;
    coordinateFontSize: number;
    yAxisMaxTicksLimit: number;
}