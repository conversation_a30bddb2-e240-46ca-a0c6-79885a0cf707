import type { CmFilterObject } from '@/views/cm-home/cm-main/components/cm-result-chart/interface';
import { type Ref, ref, watch } from 'vue';
import { GroupIdGap } from './util';
import type { CmLegend } from '@/components/cm-group-legend/interface';
import type { CustomLegendData, LineItem } from '@futurefab/ui-sdk-stores';
export const useGroupLegend = (filterInfo: Ref<CmFilterObject>, customLegendStore?: { customLegendData :CustomLegendData}, filterLotWaferData?: Ref<any[]>) => {
  const customLegendItems = ref<CmLegend[]>([]);
  const groupConfig = ref<{ id: number; key: string; color: string; }[]>([]);
  const setGroupConfig = (config: { id: number; key: string; color: string; }[]) => {
    groupConfig.value = config;
    initLengend();
  }
  const initLengend = () => {
    if (!customLegendStore?.customLegendData?.legendByCustom) {
      // 先处理 filter 数据
      const filteredGroupSet = new Set();
      if (filterInfo.value?.filterKeys?.length) {
        if (filterInfo.value.type === 'Group') {
          filterInfo.value.filterKeys.forEach(key => {
            filteredGroupSet.add(Number(key));
          });
        } else {
          filterInfo.value.filterKeys.forEach(key => {
            const [group] = (key as string).split(GroupIdGap);
              filteredGroupSet.add(Number(group));
          });
        }
      } else {
        groupConfig.value.forEach(({id}) => {
          filteredGroupSet.add(id);
        })
      };
      // traceChart 处理 filterLotWaferData
      if (filterLotWaferData?.value?.length) {
        filteredGroupSet.forEach(item => {
          const temp = filterLotWaferData?.value.some(row => Number(row.dataSet) === Number(item));
          if (!temp) {
            filteredGroupSet.delete(item);
          }
        });
      }

      customLegendItems.value = groupConfig.value
        .filter(({id}) => filteredGroupSet.has(id))
        .map(item => {
          return {
            id: item.id,
            show: true,
            color: item.color,
            name: item.key,
          } as any;
        });
    } else {
      // 根据legend 和 颜色去重
      const tempSet = new Set();
      const tempLegend: CmLegend[] = [];
      // 过滤或者全量 context 弹窗 的过滤数据
      const hasFilter = !!filterLotWaferData?.value?.length;
      let tempData: LineItem[] = hasFilter
        ? filterLotWaferData.value
        : customLegendStore.customLegendData.lineItems;
      if (filterInfo.value?.filterKeys?.length) {
        // 处理下拉 过滤数据
        tempData = tempData.filter(item => {
          if (filterInfo.value.type === 'Group') {
            // 只选 过滤的 分组
            return filterInfo.value.filterKeys.includes(item.dataSet!);
          } else {
            const keyMap: any = { 'EQP': 'eqpId', 'Chamber': 'chamber', 'Recipe': 'recipe'};
            return filterInfo.value.filterKeys.some(key => {
                const [group, context] = (key as string).split(GroupIdGap);
                const contextKey: keyof LineItem = keyMap[filterInfo.value.type];
                return Number(item.dataSet) === Number(group) && item[contextKey] === context;
            });
          }
        });
      }
      tempData.forEach(item => {
        const key = item.customLegendName + '@_@' + item.color;
        if (!tempSet.has(key)) {
          tempSet.add(key);
          tempLegend.push({
            id: key,
            name: item.customLegendName,
            color: item.color,
            show: true,
          });
        }
      });
      customLegendItems.value = tempLegend;
    }
  };
  return {
    setGroupConfig,
    customLegendItems,
    initLengend
  };
}